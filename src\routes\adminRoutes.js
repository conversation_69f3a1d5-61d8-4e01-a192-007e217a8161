const express = require('express');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const {
  createCategory,
  updateCategory,
  deleteCategory,
} = require('../controllers/categoryController');
const { commonValidation } = require('../utils/validation');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('ADMIN'));

/**
 * @swagger
 * /api/v1/admin/categories:
 *   post:
 *     summary: Create category (admin only)
 *     tags: [Admin, Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CategoryInput'
 *     responses:
 *       201:
 *         description: Category created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin role required
 */
router.post('/categories', createCategory);

/**
 * @swagger
 * /api/v1/admin/categories/{categoryId}:
 *   put:
 *     summary: Update category (admin only)
 *     tags: [Admin, Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: categoryId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CategoryInput'
 *     responses:
 *       200:
 *         description: Category updated successfully
 *       404:
 *         description: Category not found
 *       403:
 *         description: Admin role required
 */
router.put('/categories/:categoryId', commonValidation.idParam, updateCategory);

/**
 * @swagger
 * /api/v1/admin/categories/{categoryId}:
 *   delete:
 *     summary: Delete category (admin only)
 *     tags: [Admin, Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: categoryId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Category deleted successfully
 *       400:
 *         description: Category has subcategories or products
 *       404:
 *         description: Category not found
 *       403:
 *         description: Admin role required
 */
router.delete('/categories/:categoryId', commonValidation.idParam, deleteCategory);

// Placeholder for other admin routes
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'Admin dashboard - more routes to be implemented'
    }
  });
});

module.exports = router;

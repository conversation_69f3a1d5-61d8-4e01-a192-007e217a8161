import React from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import { addToComparison, removeFromComparison } from '../../store/slices/comparisonSlice';
import { Product } from '../../types';
import { ScaleIcon } from '@heroicons/react/24/outline';

interface ComparisonButtonProps {
  product: Product;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const ComparisonButton: React.FC<ComparisonButtonProps> = ({
  product,
  className = '',
  size = 'md',
  showText = false
}) => {
  const dispatch = useAppDispatch();
  const { products } = useAppSelector((state) => state.comparison);
  
  const isInComparison = products.some(p => p.id === product.id);

  const handleComparisonToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isInComparison) {
      dispatch(removeFromComparison(product.id));
    } else {
      dispatch(addToComparison(product));
    }
  };

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3'
  };

  return (
    <button
      onClick={handleComparisonToggle}
      className={`
        inline-flex items-center justify-center rounded-full transition-colors
        ${isInComparison 
          ? 'text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100' 
          : 'text-gray-400 hover:text-blue-600 bg-gray-50 hover:bg-blue-50'
        }
        ${buttonSizeClasses[size]}
        cursor-pointer
        ${className}
      `}
      title={isInComparison ? 'Remove from comparison' : 'Add to comparison'}
    >
      <ScaleIcon className={sizeClasses[size]} />
      {showText && (
        <span className="ml-2 text-sm font-medium">
          {isInComparison ? 'Remove from Compare' : 'Add to Compare'}
        </span>
      )}
    </button>
  );
};

export default ComparisonButton;

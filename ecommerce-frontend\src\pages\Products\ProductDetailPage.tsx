import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchProductById } from '../../store/slices/productSlice';
import { addToCart, fetchCart } from '../../store/slices/cartSlice';
import { addToRecentlyViewed } from '../../store/slices/recentlyViewedSlice';
import { StarIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import WishlistButton from '../../components/Products/WishlistButton';
import ComparisonButton from '../../components/Products/ComparisonButton';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();
  const { currentProduct, isLoading } = useAppSelector((state) => state.products);
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (id) {
      dispatch(fetchProductById(id));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (currentProduct) {
      // Add to recently viewed
      dispatch(addToRecentlyViewed(currentProduct));
    }
  }, [dispatch, currentProduct]);

  const handleAddToCart = async () => {
    if (!currentProduct) return;

    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    try {
      await dispatch(addToCart({ productId: currentProduct.id, quantity })).unwrap();
      dispatch(fetchCart());
    } catch (error) {
      // Error is handled by the slice and toast
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <StarIcon
            key={i}
            className={`h-5 w-5 ${
              i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="w-full h-96 bg-gray-200 rounded-lg"></div>
              <div className="grid grid-cols-4 gap-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="w-full h-20 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!currentProduct) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-8">The product you're looking for doesn't exist.</p>
          <Link
            to="/products"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Browse Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <nav className="flex mb-8" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <Link to="/" className="text-gray-700 hover:text-primary-600">
              Home
            </Link>
          </li>
          <li>
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <Link to="/products" className="text-gray-700 hover:text-primary-600">
                Products
              </Link>
            </div>
          </li>
          {currentProduct.category && (
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-gray-500">{currentProduct.category.name}</span>
              </div>
            </li>
          )}
          <li aria-current="page">
            <div className="flex items-center">
              <span className="mx-2 text-gray-400">/</span>
              <span className="text-gray-500 truncate max-w-xs">{currentProduct.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="relative overflow-hidden rounded-lg bg-gray-100">
            <img
              src={currentProduct.images[selectedImage] || 'https://via.placeholder.com/600x400'}
              alt={currentProduct.name}
              className="w-full h-96 object-cover"
            />
            {currentProduct.comparePrice && currentProduct.comparePrice > currentProduct.price && (
              <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded text-sm font-semibold">
                {Math.round(((currentProduct.comparePrice - currentProduct.price) / currentProduct.comparePrice) * 100)}% OFF
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {currentProduct.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {currentProduct.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative overflow-hidden rounded border-2 ${
                    selectedImage === index ? 'border-primary-500' : 'border-gray-200'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${currentProduct.name} ${index + 1}`}
                    className="w-full h-20 object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentProduct.name}</h1>
            {currentProduct.category && (
              <p className="text-sm text-gray-500 mt-1">
                Category: {currentProduct.category.name}
              </p>
            )}
          </div>

          {/* Rating */}
          {currentProduct.averageRating && currentProduct.averageRating > 0 && (
            <div className="flex items-center space-x-4">
              {renderStars(currentProduct.averageRating)}
              <span className="text-sm text-gray-600">
                ({currentProduct.reviewCount || 0} reviews)
              </span>
            </div>
          )}

          {/* Price */}
          <div className="flex items-center space-x-4">
            <span className="text-3xl font-bold text-gray-900">
              {formatPrice(currentProduct.price)}
            </span>
            {currentProduct.comparePrice && currentProduct.comparePrice > currentProduct.price && (
              <span className="text-xl text-gray-500 line-through">
                {formatPrice(currentProduct.comparePrice)}
              </span>
            )}
          </div>

          {/* Stock Status */}
          <div>
            {currentProduct.quantity > 0 ? (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                In Stock ({currentProduct.quantity} available)
              </span>
            ) : (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                Out of Stock
              </span>
            )}
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
            <p className="text-gray-600 leading-relaxed">
              {currentProduct.description}
            </p>
          </div>

          {/* Quantity and Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <label htmlFor="quantity" className="text-sm font-medium text-gray-700">
                Quantity:
              </label>
              <select
                id="quantity"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                disabled={currentProduct.quantity === 0}
              >
                {[...Array(Math.min(currentProduct.quantity, 10))].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={handleAddToCart}
                disabled={currentProduct.quantity === 0}
                className="flex-1 flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ShoppingCartIcon className="h-5 w-5 mr-2" />
                {currentProduct.quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <WishlistButton
                productId={currentProduct.id}
                size="lg"
                showText
                className="flex-1 justify-center"
              />
              <ComparisonButton
                product={currentProduct}
                size="lg"
                showText
                className="flex-1 justify-center"
              />
            </div>
          </div>

          {/* Product Details */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">SKU</dt>
                <dd className="text-sm text-gray-900">{currentProduct.sku}</dd>
              </div>
              {currentProduct.seller && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Seller</dt>
                  <dd className="text-sm text-gray-900">
                    {currentProduct.seller.firstName} {currentProduct.seller.lastName}
                  </dd>
                </div>
              )}
              {currentProduct.tags && currentProduct.tags.length > 0 && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Tags</dt>
                  <dd className="text-sm text-gray-900">
                    <div className="flex flex-wrap gap-2 mt-1">
                      {currentProduct.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;

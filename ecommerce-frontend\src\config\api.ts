import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import securityConfig, { getSecurityHeaders, SecureStorage, InputSanitizer } from './security';

// API Configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';

// Create axios instance with enhanced security
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: securityConfig.api.timeout,
  headers: {
    'Content-Type': 'application/json',
    ...getSecurityHeaders(),
  },
  withCredentials: true, // Include cookies for CSRF protection
});

// Enhanced secure token management
export const tokenManager = {
  getAccessToken: (): string | null => {
    return SecureStorage.getItem(securityConfig.storage.tokenStorageKey);
  },

  setAccessToken: (token: string): void => {
    SecureStorage.setItem(securityConfig.storage.tokenStorageKey, token);
  },

  getRefreshToken: (): string | null => {
    return SecureStorage.getItem(securityConfig.storage.refreshTokenStorageKey);
  },

  setRefreshToken: (token: string): void => {
    SecureStorage.setItem(securityConfig.storage.refreshTokenStorageKey, token);
  },

  clearTokens: (): void => {
    SecureStorage.removeItem(securityConfig.storage.tokenStorageKey);
    SecureStorage.removeItem(securityConfig.storage.refreshTokenStorageKey);
  },

  setTokens: (accessToken: string, refreshToken: string): void => {
    SecureStorage.setItem(securityConfig.storage.tokenStorageKey, accessToken);
    SecureStorage.setItem(securityConfig.storage.refreshTokenStorageKey, refreshToken);
  },

  // Check if token is about to expire
  isTokenExpiringSoon: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      return (expirationTime - currentTime) < securityConfig.auth.tokenRefreshThreshold;
    } catch {
      return true; // If we can't parse the token, consider it expired
    }
  }
};

// Enhanced request interceptor with security features
api.interceptors.request.use(
  (config: AxiosRequestConfig): any => {
    // Add authentication token
    const token = tokenManager.getAccessToken();
    if (token && config.headers) {
      // Check if token is expiring soon and refresh if needed
      if (tokenManager.isTokenExpiringSoon(token)) {
        // Token refresh will be handled by the response interceptor
      }
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add security headers
    if (config.headers) {
      config.headers = {
        ...config.headers,
        ...getSecurityHeaders(),
        'X-Requested-With': 'XMLHttpRequest', // CSRF protection
      };
    }

    // Sanitize request data
    if (config.data && typeof config.data === 'object') {
      config.data = sanitizeRequestData(config.data);
    }

    // Add request timestamp for replay attack protection
    if (config.headers) {
      config.headers['X-Request-Time'] = Date.now().toString();
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Helper function to sanitize request data
const sanitizeRequestData = (data: any): any => {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sanitized: any = Array.isArray(data) ? [] : {};

  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const value = data[key];

      if (typeof value === 'string') {
        // Sanitize string values based on field type
        if (key.toLowerCase().includes('email')) {
          sanitized[key] = InputSanitizer.sanitizeEmail(value);
        } else if (key.toLowerCase().includes('search') || key.toLowerCase().includes('query')) {
          sanitized[key] = InputSanitizer.sanitizeSearchQuery(value);
        } else if (key.toLowerCase().includes('url')) {
          sanitized[key] = InputSanitizer.sanitizeUrl(value);
        } else {
          sanitized[key] = InputSanitizer.sanitizeHtml(value);
        }
      } else if (typeof value === 'object') {
        sanitized[key] = sanitizeRequestData(value);
      } else {
        sanitized[key] = value;
      }
    }
  }

  return sanitized;
};

// Enhanced response interceptor with security and retry logic
let refreshTokenPromise: Promise<any> | null = null;

api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log successful responses for security monitoring
    if (process.env.NODE_ENV === 'development') {
      console.log(`API Success: ${response.config.method?.toUpperCase()} ${response.config.url}`);
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (token expired) with retry logic
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Prevent multiple simultaneous refresh requests
        if (!refreshTokenPromise) {
          const refreshToken = tokenManager.getRefreshToken();
          if (refreshToken) {
            refreshTokenPromise = axios.post(`${API_BASE_URL}/auth/refresh`, {
              refreshToken,
            }, {
              timeout: 10000, // 10 second timeout for refresh
              headers: getSecurityHeaders(),
            });
          } else {
            throw new Error('No refresh token available');
          }
        }

        const response = await refreshTokenPromise;
        refreshTokenPromise = null; // Reset the promise

        if (response.data.success) {
          const { accessToken, refreshToken: newRefreshToken } = response.data.data.tokens;
          tokenManager.setTokens(accessToken, newRefreshToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        refreshTokenPromise = null; // Reset the promise
        // Refresh failed, clear tokens and redirect to login
        tokenManager.clearTokens();

        // Only redirect if not already on login page
        if (!window.location.pathname.includes('/login')) {
          toast.error('Session expired. Please log in again.');
          window.location.href = '/login';
        }
        return Promise.reject(refreshError);
      }
    }

    // Enhanced error handling with security considerations
    if (error.response) {
      const { status, data } = error.response;

      // Log security-relevant errors
      if ([401, 403, 429].includes(status)) {
        console.warn(`Security-relevant error: ${status} - ${data.error?.message || 'Unknown error'}`);
      }

      switch (status) {
        case 400:
          if (data.error?.details) {
            // Validation errors
            const validationErrors = data.error.details
              .map((detail: any) => detail.message)
              .join(', ');
            toast.error(validationErrors);
          } else {
            toast.error(data.error?.message || 'Bad request');
          }
          break;

        case 401:
          // Don't show error if we're already handling token refresh
          if (!originalRequest._retry) {
            toast.error('Authentication required');
            tokenManager.clearTokens();
            if (!window.location.pathname.includes('/login')) {
              window.location.href = '/login';
            }
          }
          break;

        case 403:
          toast.error(data.error?.message || 'Access denied');
          break;

        case 404:
          toast.error('Resource not found');
          break;

        case 422:
          toast.error(data.error?.message || 'Validation failed');
          break;

        case 423:
          // Account locked
          toast.error(data.error?.message || 'Account is temporarily locked');
          break;

        case 429:
          const retryAfter = error.response.headers['retry-after'];
          const message = retryAfter
            ? `Too many requests. Please try again in ${retryAfter} seconds.`
            : 'Too many requests. Please try again later.';
          toast.error(message);
          break;

        case 500:
          toast.error('Server error. Please try again later.');
          break;

        case 502:
        case 503:
        case 504:
          toast.error('Service temporarily unavailable. Please try again later.');
          break;

        default:
          // Don't expose internal error details in production
          const message = process.env.NODE_ENV === 'development'
            ? data.error?.message || `HTTP ${status} error`
            : 'An error occurred. Please try again.';
          toast.error(message);
      }
    } else if (error.request) {
      // Network error - could be CORS, timeout, or connection issue
      if (error.code === 'ECONNABORTED') {
        toast.error('Request timeout. Please try again.');
      } else {
        toast.error('Network error. Please check your connection.');
      }
    } else {
      // Other error (request setup, etc.)
      const message = process.env.NODE_ENV === 'development'
        ? error.message
        : 'An unexpected error occurred';
      toast.error(message);
    }

    return Promise.reject(error);
  }
);

// API helper functions
export const apiHelpers = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.get(url, config);
    return response.data as T;
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.post(url, data, config);
    return response.data as T;
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.put(url, data, config);
    return response.data as T;
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.delete(url, config);
    return response.data as T;
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.patch(url, data, config);
    return response.data as T;
  },
};

// Query parameter builder
export const buildQueryParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
};

// Enhanced file upload helper with security validation
export const uploadFile = async (
  file: File,
  endpoint: string,
  onProgress?: (progress: number) => void
): Promise<any> => {
  // Validate file before upload
  if (!validateFile(file)) {
    throw new Error('Invalid file type or size');
  }

  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
    timeout: 60000, // 60 seconds for file uploads
  });

  return response.data;
};

// File validation helper
const validateFile = (file: File): boolean => {
  // Check file size
  if (file.size > securityConfig.validation.maxFileSize) {
    toast.error(`File size must be less than ${Math.round(securityConfig.validation.maxFileSize / (1024 * 1024))}MB`);
    return false;
  }

  // Check file type
  if (!securityConfig.validation.allowedFileTypes.includes(file.type)) {
    toast.error('File type not allowed');
    return false;
  }

  // Check file name for suspicious patterns
  const suspiciousPatterns = [
    /\.(exe|bat|cmd|scr|pif|com)$/i, // Executable files
    /[<>:"|?*]/, // Invalid filename characters
    /^\./, // Hidden files
    /\.(php|jsp|asp|aspx|js|html|htm)$/i, // Script files
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      toast.error('Invalid file name or type');
      return false;
    }
  }

  return true;
};

export default api;

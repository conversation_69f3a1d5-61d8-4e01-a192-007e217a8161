# E-Commerce Backend API

A production-ready e-commerce backend API built with Express.js and PostgreSQL, featuring comprehensive user management, product catalog, order processing, and admin functionality.

## Features

### 🔐 Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (Customer, Seller, Admin)
- Password reset functionality
- Email/phone verification system
- Secure password hashing with bcrypt

### 👥 User Management
- User registration and profile management
- Multiple shipping/billing address support
- Default address selection
- User role management

### 📦 Product Management
- CRUD operations for products (sellers only)
- Product categories and subcategories
- Product variants (color, size, etc.)
- Image upload and management
- Stock tracking
- Admin approval workflow

### 🔍 Search & Discovery
- Full-text search functionality
- Advanced filtering (category, price, rating, availability)
- Sorting options (price, date, popularity, rating)
- Pagination for large result sets

### 🛒 Shopping Cart & Wishlist
- Persistent cart functionality
- Quantity management
- Wishlist/favorites system

### 📋 Order Processing
- Complete checkout flow
- Address selection and validation
- Coupon/discount code system
- Payment integration ready (Stripe)
- Order confirmation and invoice generation

### ⭐ Review & Rating System
- Verified purchase requirement for reviews
- 1-5 star rating system
- Review moderation capabilities
- Image uploads for reviews

### 🛡️ Security & Performance
- Rate limiting implementation
- CORS configuration
- Input validation and sanitization
- SQL injection prevention
- Audit logging for sensitive operations

## Tech Stack

- **Backend Framework**: Express.js
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: JWT (jsonwebtoken)
- **Password Hashing**: bcryptjs
- **Validation**: express-validator
- **File Upload**: multer
- **Email**: nodemailer
- **Payment**: Stripe (ready for integration)
- **Logging**: winston
- **API Documentation**: Swagger/OpenAPI

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ecommerce-backend-api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   - Database connection string
   - JWT secrets
   - Email configuration
   - Stripe keys (for payments)

4. **Set up PostgreSQL database**
   ```bash
   # Create database
   createdb ecommerce_db
   ```

5. **Run database migrations**
   ```bash
   npm run db:migrate
   ```

6. **Generate Prisma client**
   ```bash
   npm run db:generate
   ```

7. **Seed the database (optional)**
   ```bash
   npm run db:seed
   ```

## Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

The API will be available at `http://localhost:3000`

## API Documentation

When running in development mode, API documentation is available at:
`http://localhost:3000/api-docs`

## Database Schema

The application uses a comprehensive database schema with the following main entities:

- **Users**: Customer, seller, and admin accounts
- **Products**: Product catalog with variants and categories
- **Orders**: Order processing and tracking
- **Reviews**: Product reviews and ratings
- **Cart**: Shopping cart items
- **Addresses**: User shipping/billing addresses
- **Coupons**: Discount codes and promotions
- **Audit Logs**: Security and activity tracking

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/profile` - Get user profile

### Products
- `GET /api/v1/products` - List products with filtering
- `GET /api/v1/products/:id` - Get product details
- `POST /api/v1/products` - Create product (sellers only)
- `PUT /api/v1/products/:id` - Update product (sellers only)
- `DELETE /api/v1/products/:id` - Delete product (sellers only)

### Orders
- `GET /api/v1/orders` - Get user orders
- `POST /api/v1/orders` - Create new order
- `GET /api/v1/orders/:id` - Get order details
- `PUT /api/v1/orders/:id/status` - Update order status

### Cart
- `GET /api/v1/cart` - Get cart items
- `POST /api/v1/cart` - Add item to cart
- `PUT /api/v1/cart/:id` - Update cart item
- `DELETE /api/v1/cart/:id` - Remove cart item

### Admin
- `GET /api/v1/admin/users` - Manage users
- `GET /api/v1/admin/products` - Manage products
- `GET /api/v1/admin/orders` - Manage orders
- `GET /api/v1/admin/analytics` - View analytics

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with hot reload
- `npm test` - Run tests
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed database with sample data
- `npm run db:reset` - Reset database
- `npm run db:studio` - Open Prisma Studio
- `npm run lint` - Run ESLint
- `npm run docs` - Generate API documentation

## Environment Variables

Key environment variables (see `.env.example` for complete list):

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - JWT signing secret
- `JWT_REFRESH_SECRET` - Refresh token secret
- `STRIPE_SECRET_KEY` - Stripe secret key
- `SMTP_*` - Email configuration
- `CORS_ORIGIN` - Allowed CORS origins

## Security Features

- Password hashing with bcrypt (12 rounds)
- JWT token authentication with refresh tokens
- Rate limiting to prevent abuse
- Input validation and sanitization
- SQL injection prevention with Prisma
- CORS protection
- Helmet.js security headers
- Audit logging for sensitive operations

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

## Deployment

The application is ready for deployment to various platforms:

- **Heroku**: Use the included `Procfile`
- **Docker**: Dockerfile included
- **AWS/GCP/Azure**: Compatible with all major cloud providers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact [<EMAIL>](mailto:<EMAIL>)

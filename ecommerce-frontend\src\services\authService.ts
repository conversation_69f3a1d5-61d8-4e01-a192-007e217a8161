import { apiHelpers } from '../config/api';
import {
  ApiResponse,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  User,
} from '../types';

export const authService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    return apiHelpers.post('/auth/login', credentials);
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    return apiHelpers.post('/auth/register', userData);
  },

  // Logout user
  logout: async (refreshToken: string): Promise<ApiResponse<{ message: string }>> => {
    return apiHelpers.post('/auth/logout', { refreshToken });
  },

  // Get current user profile
  getProfile: async (): Promise<ApiResponse<{ user: User }>> => {
    return apiHelpers.get('/auth/profile');
  },

  // Verify email
  verifyEmail: async (token: string): Promise<ApiResponse<{ message: string }>> => {
    return apiHelpers.post('/auth/verify-email', { token });
  },

  // Request password reset
  requestPasswordReset: async (email: string): Promise<ApiResponse<{ message: string }>> => {
    return apiHelpers.post('/auth/password-reset/request', { email });
  },

  // Confirm password reset
  confirmPasswordReset: async (
    token: string,
    password: string
  ): Promise<ApiResponse<{ message: string }>> => {
    return apiHelpers.post('/auth/password-reset/confirm', { token, password });
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ tokens: any }>> => {
    return apiHelpers.post('/auth/refresh', { refreshToken });
  },
};

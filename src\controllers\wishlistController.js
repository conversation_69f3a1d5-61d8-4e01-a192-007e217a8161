const { prisma } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Get user's wishlist
 */
const getWishlist = async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20 } = req.query;

  try {
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [wishlistItems, totalCount] = await Promise.all([
      prisma().wishlistItem.findMany({
        where: { userId },
        include: {
          product: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                }
              },
              seller: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                }
              },
              reviews: {
                select: {
                  rating: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: parseInt(limit),
      }),
      prisma().wishlistItem.count({ where: { userId } })
    ]);

    // Calculate average ratings for products
    const wishlistWithRatings = wishlistItems.map(item => ({
      ...item,
      product: {
        ...item.product,
        averageRating: item.product.reviews.length > 0 
          ? item.product.reviews.reduce((sum, review) => sum + review.rating, 0) / item.product.reviews.length
          : 0,
        reviewCount: item.product.reviews.length,
        reviews: undefined, // Remove reviews array from response
      }
    }));

    const totalPages = Math.ceil(totalCount / parseInt(limit));

    res.json({
      success: true,
      data: {
        wishlist: wishlistWithRatings,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        }
      }
    });

  } catch (error) {
    logger.error('Get wishlist error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get wishlist'
      }
    });
  }
};

/**
 * Add product to wishlist
 */
const addToWishlist = async (req, res) => {
  const userId = req.user.id;
  const { productId } = req.body;

  try {
    // Check if product exists
    const product = await prisma().product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, status: true }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found'
        }
      });
    }

    if (product.status !== 'APPROVED') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Product is not available'
        }
      });
    }

    // Check if already in wishlist
    const existingItem = await prisma().wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    });

    if (existingItem) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Product already in wishlist'
        }
      });
    }

    // Add to wishlist
    const wishlistItem = await prisma().wishlistItem.create({
      data: {
        userId,
        productId
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            images: true,
          }
        }
      }
    });

    logger.info(`Product added to wishlist: ${product.name} for user: ${req.user.email}`);

    res.status(201).json({
      success: true,
      data: {
        wishlistItem
      }
    });

  } catch (error) {
    logger.error('Add to wishlist error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to add product to wishlist'
      }
    });
  }
};

/**
 * Remove product from wishlist
 */
const removeFromWishlist = async (req, res) => {
  const userId = req.user.id;
  const { productId } = req.params;

  try {
    // Check if item exists in wishlist
    const wishlistItem = await prisma().wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      },
      include: {
        product: {
          select: {
            name: true
          }
        }
      }
    });

    if (!wishlistItem) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found in wishlist'
        }
      });
    }

    // Remove from wishlist
    await prisma().wishlistItem.delete({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    });

    logger.info(`Product removed from wishlist: ${wishlistItem.product.name} for user: ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Product removed from wishlist'
      }
    });

  } catch (error) {
    logger.error('Remove from wishlist error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to remove product from wishlist'
      }
    });
  }
};

/**
 * Check if product is in wishlist
 */
const checkWishlistStatus = async (req, res) => {
  const userId = req.user.id;
  const { productId } = req.params;

  try {
    const wishlistItem = await prisma().wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    });

    res.json({
      success: true,
      data: {
        inWishlist: !!wishlistItem
      }
    });

  } catch (error) {
    logger.error('Check wishlist status error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to check wishlist status'
      }
    });
  }
};

/**
 * Clear entire wishlist
 */
const clearWishlist = async (req, res) => {
  const userId = req.user.id;

  try {
    const deletedCount = await prisma().wishlistItem.deleteMany({
      where: { userId }
    });

    logger.info(`Wishlist cleared for user: ${req.user.email}, ${deletedCount.count} items removed`);

    res.json({
      success: true,
      data: {
        message: `Removed ${deletedCount.count} items from wishlist`
      }
    });

  } catch (error) {
    logger.error('Clear wishlist error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to clear wishlist'
      }
    });
  }
};

module.exports = {
  getWishlist,
  addToWishlist,
  removeFromWishlist,
  checkWishlistStatus,
  clearWishlist,
};

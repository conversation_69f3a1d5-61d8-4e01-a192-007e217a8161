/**
 * Frontend Security Configuration
 */

export interface SecurityConfig {
  api: {
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  auth: {
    tokenRefreshThreshold: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  validation: {
    maxInputLength: number;
    allowedFileTypes: string[];
    maxFileSize: number;
  };
  storage: {
    encryptLocalStorage: boolean;
    tokenStorageKey: string;
    refreshTokenStorageKey: string;
  };
  csp: {
    reportUri?: string;
    reportOnly: boolean;
  };
}

const securityConfig: SecurityConfig = {
  api: {
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },
  auth: {
    tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes before expiry
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },
  validation: {
    maxInputLength: 1000,
    allowedFileTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf'
    ],
    maxFileSize: 5 * 1024 * 1024, // 5MB
  },
  storage: {
    encryptLocalStorage: process.env.NODE_ENV === 'production',
    tokenStorageKey: 'ecommerce_token',
    refreshTokenStorageKey: 'ecommerce_refresh_token',
  },
  csp: {
    reportOnly: process.env.NODE_ENV !== 'production',
  }
};

/**
 * Content Security Policy configuration
 */
export const getCSPDirectives = () => {
  const baseDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for React development
      process.env.NODE_ENV === 'development' ? "'unsafe-eval'" : null,
    ].filter(Boolean),
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and Tailwind
      'https://fonts.googleapis.com',
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
    ],
    'img-src': [
      "'self'",
      'data:',
      'https:',
      'blob:',
    ],
    'connect-src': [
      "'self'",
      process.env.REACT_APP_API_URL || 'http://localhost:3000',
    ],
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': process.env.NODE_ENV === 'production' ? [] : null,
  };

  // Remove null values
  return Object.fromEntries(
    Object.entries(baseDirectives).filter(([_, value]) => value !== null)
  );
};

/**
 * Security headers for API requests
 */
export const getSecurityHeaders = () => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  };
};

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    // Basic HTML entity encoding
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize for URL usage
   */
  static sanitizeUrl(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    // Remove dangerous protocols
    const dangerousProtocols = /^(javascript|data|vbscript|file|ftp):/i;
    if (dangerousProtocols.test(input)) {
      return '';
    }
    
    return encodeURIComponent(input);
  }

  /**
   * Sanitize search query
   */
  static sanitizeSearchQuery(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    // Remove SQL injection patterns and limit length
    return input
      .replace(/[<>'"]/g, '')
      .replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi, '')
      .replace(/(--|\/\*|\*\/|;)/g, '')
      .trim()
      .substring(0, 100);
  }

  /**
   * Validate and sanitize email
   */
  static sanitizeEmail(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const sanitized = input.toLowerCase().trim();
    
    return emailRegex.test(sanitized) ? sanitized : '';
  }
}

/**
 * Secure storage utilities
 */
export class SecureStorage {
  private static encryptionKey = 'ecommerce-security-key';

  /**
   * Simple encryption for local storage (basic obfuscation)
   */
  private static encrypt(text: string): string {
    if (!securityConfig.storage.encryptLocalStorage) {
      return text;
    }
    
    // Simple XOR encryption for basic obfuscation
    let result = '';
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(
        text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
      );
    }
    return btoa(result);
  }

  /**
   * Simple decryption for local storage
   */
  private static decrypt(encryptedText: string): string {
    if (!securityConfig.storage.encryptLocalStorage) {
      return encryptedText;
    }
    
    try {
      const text = atob(encryptedText);
      let result = '';
      for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(
          text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
        );
      }
      return result;
    } catch {
      return '';
    }
  }

  /**
   * Securely store data in localStorage
   */
  static setItem(key: string, value: string): void {
    try {
      const encrypted = this.encrypt(value);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store data securely:', error);
    }
  }

  /**
   * Securely retrieve data from localStorage
   */
  static getItem(key: string): string | null {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      return this.decrypt(encrypted);
    } catch (error) {
      console.error('Failed to retrieve data securely:', error);
      return null;
    }
  }

  /**
   * Remove item from localStorage
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove data:', error);
    }
  }

  /**
   * Clear all stored data
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }
}

/**
 * Rate limiting for client-side actions
 */
export class ClientRateLimit {
  private static attempts: Map<string, { count: number; resetTime: number }> = new Map();

  /**
   * Check if action is rate limited
   */
  static isLimited(action: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
    const now = Date.now();
    const key = action;
    const attempt = this.attempts.get(key);

    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs });
      return false;
    }

    if (attempt.count >= maxAttempts) {
      return true;
    }

    attempt.count++;
    return false;
  }

  /**
   * Reset rate limit for action
   */
  static reset(action: string): void {
    this.attempts.delete(action);
  }
}

export default securityConfig;

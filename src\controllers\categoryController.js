const { prisma } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Generate unique slug from category name
 */
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

/**
 * Get all categories with hierarchy
 */
const getCategories = async (req, res) => {
  try {
    const { includeInactive = false } = req.query;

    const where = {
      ...(includeInactive === 'false' && { isActive: true })
    };

    // Get parent categories first
    const parentCategories = await prisma().category.findMany({
      where: {
        ...where,
        parentId: null
      },
      include: {
        children: {
          where,
          orderBy: { sortOrder: 'asc' },
          include: {
            _count: {
              select: {
                products: {
                  where: { status: 'APPROVED' }
                }
              }
            }
          }
        },
        _count: {
          select: {
            products: {
              where: { status: 'APPROVED' }
            }
          }
        }
      },
      orderBy: { sortOrder: 'asc' }
    });

    res.json({
      success: true,
      data: {
        categories: parentCategories
      }
    });

  } catch (error) {
    logger.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get categories'
      }
    });
  }
};

/**
 * Get single category with products
 */
const getCategory = async (req, res) => {
  const { categoryId } = req.params;

  try {
    const category = await prisma().category.findUnique({
      where: { id: categoryId },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        children: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
            image: true,
            _count: {
              select: {
                products: {
                  where: { status: 'APPROVED' }
                }
              }
            }
          },
          orderBy: { sortOrder: 'asc' }
        },
        _count: {
          select: {
            products: {
              where: { status: 'APPROVED' }
            }
          }
        }
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Category not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        category
      }
    });

  } catch (error) {
    logger.error('Get category error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get category'
      }
    });
  }
};

/**
 * Create category (admin only)
 */
const createCategory = async (req, res) => {
  try {
    const {
      name,
      description,
      image,
      parentId,
      sortOrder = 0
    } = req.body;

    // Generate unique slug
    let slug = generateSlug(name);
    
    const existingSlug = await prisma().category.findUnique({
      where: { slug }
    });

    if (existingSlug) {
      slug = `${slug}-${Date.now()}`;
    }

    // Verify parent category exists if provided
    if (parentId) {
      const parentCategory = await prisma().category.findUnique({
        where: { id: parentId }
      });

      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Parent category not found'
          }
        });
      }
    }

    const category = await prisma().category.create({
      data: {
        name,
        slug,
        description,
        image,
        parentId,
        sortOrder: parseInt(sortOrder),
        isActive: true
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    logger.info(`Category created: ${category.name} by ${req.user.email}`);

    res.status(201).json({
      success: true,
      data: {
        category
      }
    });

  } catch (error) {
    logger.error('Create category error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create category'
      }
    });
  }
};

/**
 * Update category (admin only)
 */
const updateCategory = async (req, res) => {
  const { categoryId } = req.params;
  const updateData = req.body;

  try {
    const existingCategory = await prisma().category.findUnique({
      where: { id: categoryId }
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Category not found'
        }
      });
    }

    // If updating name, regenerate slug
    if (updateData.name && updateData.name !== existingCategory.name) {
      let newSlug = generateSlug(updateData.name);
      
      const existingSlug = await prisma().category.findFirst({
        where: {
          slug: newSlug,
          id: { not: categoryId }
        }
      });

      if (existingSlug) {
        newSlug = `${newSlug}-${Date.now()}`;
      }

      updateData.slug = newSlug;
    }

    // Verify parent category exists if provided
    if (updateData.parentId && updateData.parentId !== existingCategory.parentId) {
      const parentCategory = await prisma().category.findUnique({
        where: { id: updateData.parentId }
      });

      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Parent category not found'
          }
        });
      }

      // Prevent circular reference
      if (updateData.parentId === categoryId) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Category cannot be its own parent'
          }
        });
      }
    }

    // Convert numeric fields
    if (updateData.sortOrder) updateData.sortOrder = parseInt(updateData.sortOrder);

    const updatedCategory = await prisma().category.update({
      where: { id: categoryId },
      data: updateData,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    logger.info(`Category updated: ${updatedCategory.name} by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        category: updatedCategory
      }
    });

  } catch (error) {
    logger.error('Update category error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update category'
      }
    });
  }
};

/**
 * Delete category (admin only)
 */
const deleteCategory = async (req, res) => {
  const { categoryId } = req.params;

  try {
    const existingCategory = await prisma().category.findUnique({
      where: { id: categoryId },
      include: {
        children: true,
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Category not found'
        }
      });
    }

    // Check if category has children
    if (existingCategory.children.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Cannot delete category with subcategories'
        }
      });
    }

    // Check if category has products
    if (existingCategory._count.products > 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Cannot delete category with products'
        }
      });
    }

    await prisma().category.delete({
      where: { id: categoryId }
    });

    logger.info(`Category deleted: ${existingCategory.name} by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Category deleted successfully'
      }
    });

  } catch (error) {
    logger.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete category'
      }
    });
  }
};

module.exports = {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
};

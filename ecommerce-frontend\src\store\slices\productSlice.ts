import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, Category, ProductFilters, PaginationMeta } from '../../types';
import { apiHelpers, buildQueryParams } from '../../config/api';

interface ProductState {
  products: Product[];
  categories: Category[];
  currentProduct: Product | null;
  filters: ProductFilters;
  pagination: PaginationMeta | null;
  searchResults: Product[];
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  featuredProducts: Product[];
}

const initialState: ProductState = {
  products: [],
  categories: [],
  currentProduct: null,
  filters: {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  pagination: null,
  searchResults: [],
  searchQuery: '',
  isLoading: false,
  error: null,
  featuredProducts: [],
};

// Async thunks
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters: ProductFilters, { rejectWithValue }) => {
    try {
      const queryString = buildQueryParams(filters);
      const response: any = await apiHelpers.get(`/products?${queryString}`);
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch products');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch products');
    }
  }
);

export const fetchProduct = createAsyncThunk(
  'products/fetchProduct',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.get(`/products/${productId}`);
      if (response.success) {
        return response.data.product;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch product');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch product');
    }
  }
);

export const searchProducts = createAsyncThunk(
  'products/searchProducts',
  async (params: { q: string; filters?: Partial<ProductFilters> }, { rejectWithValue }) => {
    try {
      const { q, filters = {} } = params;
      const queryString = buildQueryParams({ q, ...filters });
      const response: any = await apiHelpers.get(`/products/search?${queryString}`);
      if (response.success) {
        return { products: response.data.products, query: q };
      }
      return rejectWithValue(response.error?.message || 'Failed to search products');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to search products');
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.get('/categories');
      if (response.success) {
        return response.data.categories;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch categories');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch categories');
    }
  }
);

export const fetchFeaturedProducts = createAsyncThunk(
  'products/fetchFeaturedProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.get('/products?featured=true&limit=12');
      if (response.success) {
        return response.data.products;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch featured products');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch featured products');
    }
  }
);

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<Partial<ProductFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };
    },
    setCurrentProduct: (state, action: PayloadAction<Product | null>) => {
      state.currentProduct = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
      state.searchQuery = '';
    },
    updateProductInList: (state, action: PayloadAction<Product>) => {
      const updatedProduct = action.payload;
      const index = state.products.findIndex(p => p.id === updatedProduct.id);
      if (index !== -1) {
        state.products[index] = updatedProduct;
      }
      
      // Update in featured products if exists
      const featuredIndex = state.featuredProducts.findIndex(p => p.id === updatedProduct.id);
      if (featuredIndex !== -1) {
        state.featuredProducts[featuredIndex] = updatedProduct;
      }
      
      // Update current product if it's the same
      if (state.currentProduct?.id === updatedProduct.id) {
        state.currentProduct = updatedProduct;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch products
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.products = action.payload.products;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single product
    builder
      .addCase(fetchProduct.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload;
        state.error = null;
      })
      .addCase(fetchProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Search products
    builder
      .addCase(searchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload.products;
        state.searchQuery = action.payload.query;
        state.error = null;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch categories
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload;
        state.error = null;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch featured products
    builder
      .addCase(fetchFeaturedProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFeaturedProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.featuredProducts = action.payload;
        state.error = null;
      })
      .addCase(fetchFeaturedProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setFilters,
  clearFilters,
  setCurrentProduct,
  clearSearchResults,
  updateProductInList,
} = productSlice.actions;

// Alias for fetchProduct to match the component usage
export const fetchProductById = fetchProduct;

export default productSlice.reducer;

const { prisma } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Get user's cart items
 */
const getCart = async (req, res) => {
  const userId = req.user.id;

  try {
    const cartItems = await prisma().cartItem.findMany({
      where: { userId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            images: true,
            status: true,
            quantity: true,
            trackQuantity: true,
            allowBackorder: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        },
        variant: {
          select: {
            id: true,
            name: true,
            price: true,
            quantity: true,
            options: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate totals
    let subtotal = 0;
    let totalItems = 0;

    const cartWithCalculations = cartItems.map(item => {
      const price = item.variant?.price || item.product.price;
      const itemTotal = parseFloat(price) * item.quantity;
      subtotal += itemTotal;
      totalItems += item.quantity;

      return {
        ...item,
        unitPrice: price,
        totalPrice: itemTotal,
        isAvailable: item.product.status === 'APPROVED' && 
                    (item.product.trackQuantity ? 
                      (item.variant ? item.variant.quantity >= item.quantity : item.product.quantity >= item.quantity) :
                      true)
      };
    });

    res.json({
      success: true,
      data: {
        cartItems: cartWithCalculations,
        summary: {
          totalItems,
          subtotal: parseFloat(subtotal.toFixed(2)),
          itemCount: cartItems.length
        }
      }
    });

  } catch (error) {
    logger.error('Get cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get cart'
      }
    });
  }
};

/**
 * Add item to cart
 */
const addToCart = async (req, res) => {
  const { productId, variantId, quantity = 1 } = req.body;
  const userId = req.user.id;

  try {
    // Verify product exists and is available
    const product = await prisma().product.findUnique({
      where: { id: productId },
      include: {
        variants: variantId ? {
          where: { id: variantId }
        } : false
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found'
        }
      });
    }

    if (product.status !== 'APPROVED') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Product is not available'
        }
      });
    }

    // Check variant if specified
    let variant = null;
    if (variantId) {
      variant = product.variants.find(v => v.id === variantId);
      if (!variant) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Product variant not found'
          }
        });
      }
    }

    // Check stock availability
    const availableQuantity = variant ? variant.quantity : product.quantity;
    if (product.trackQuantity && !product.allowBackorder && availableQuantity < quantity) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Only ${availableQuantity} items available in stock`
        }
      });
    }

    // Check if item already exists in cart
    const existingCartItem = await prisma().cartItem.findFirst({
      where: {
        userId,
        productId,
        variantId: variantId || null
      }
    });

    let cartItem;

    if (existingCartItem) {
      // Update quantity
      const newQuantity = existingCartItem.quantity + parseInt(quantity);
      
      // Check stock for new quantity
      if (product.trackQuantity && !product.allowBackorder && availableQuantity < newQuantity) {
        return res.status(400).json({
          success: false,
          error: {
            message: `Only ${availableQuantity} items available in stock`
          }
        });
      }

      cartItem = await prisma().cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: newQuantity },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              price: true,
              images: true
            }
          },
          variant: {
            select: {
              id: true,
              name: true,
              price: true,
              options: true
            }
          }
        }
      });
    } else {
      // Create new cart item
      cartItem = await prisma().cartItem.create({
        data: {
          userId,
          productId,
          variantId,
          quantity: parseInt(quantity)
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              price: true,
              images: true
            }
          },
          variant: {
            select: {
              id: true,
              name: true,
              price: true,
              options: true
            }
          }
        }
      });
    }

    logger.info(`Item added to cart: ${product.name} by ${req.user.email}`);

    res.status(201).json({
      success: true,
      data: {
        cartItem,
        message: 'Item added to cart successfully'
      }
    });

  } catch (error) {
    logger.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to add item to cart'
      }
    });
  }
};

/**
 * Update cart item quantity
 */
const updateCartItem = async (req, res) => {
  const { cartItemId } = req.params;
  const { quantity } = req.body;
  const userId = req.user.id;

  try {
    // Get cart item
    const cartItem = await prisma().cartItem.findFirst({
      where: {
        id: cartItemId,
        userId
      },
      include: {
        product: true,
        variant: true
      }
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Cart item not found'
        }
      });
    }

    // Check stock availability
    const availableQuantity = cartItem.variant ? cartItem.variant.quantity : cartItem.product.quantity;
    if (cartItem.product.trackQuantity && !cartItem.product.allowBackorder && availableQuantity < quantity) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Only ${availableQuantity} items available in stock`
        }
      });
    }

    const updatedCartItem = await prisma().cartItem.update({
      where: { id: cartItemId },
      data: { quantity: parseInt(quantity) },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            images: true
          }
        },
        variant: {
          select: {
            id: true,
            name: true,
            price: true,
            options: true
          }
        }
      }
    });

    logger.info(`Cart item updated by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        cartItem: updatedCartItem
      }
    });

  } catch (error) {
    logger.error('Update cart item error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update cart item'
      }
    });
  }
};

/**
 * Remove item from cart
 */
const removeFromCart = async (req, res) => {
  const { cartItemId } = req.params;
  const userId = req.user.id;

  try {
    // Check if cart item belongs to user
    const cartItem = await prisma().cartItem.findFirst({
      where: {
        id: cartItemId,
        userId
      }
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Cart item not found'
        }
      });
    }

    await prisma().cartItem.delete({
      where: { id: cartItemId }
    });

    logger.info(`Cart item removed by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Item removed from cart successfully'
      }
    });

  } catch (error) {
    logger.error('Remove from cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to remove item from cart'
      }
    });
  }
};

/**
 * Clear entire cart
 */
const clearCart = async (req, res) => {
  const userId = req.user.id;

  try {
    await prisma().cartItem.deleteMany({
      where: { userId }
    });

    logger.info(`Cart cleared by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Cart cleared successfully'
      }
    });

  } catch (error) {
    logger.error('Clear cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to clear cart'
      }
    });
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
};

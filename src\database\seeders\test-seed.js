const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSeed() {
  try {
    console.log('Starting test seeding...');

    // Create a simple admin user
    const adminPassword = await bcrypt.hash('Admin123!@#', 12);
    
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        status: 'ACTIVE',
        emailVerified: true,
      },
    });

    console.log(`Admin user created: ${admin.email}`);

    // Create a simple category
    const category = await prisma.category.upsert({
      where: { slug: 'electronics' },
      update: {},
      create: {
        name: 'Electronics',
        slug: 'electronics',
        description: 'Electronic devices and accessories',
        image: '/uploads/categories/electronics.jpg',
      },
    });

    console.log(`Category created: ${category.name}`);

    console.log('Test seeding completed successfully!');
  } catch (error) {
    console.error('Error in test seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

testSeed()
  .catch((error) => {
    console.error('Test seeding failed:', error);
    process.exit(1);
  });

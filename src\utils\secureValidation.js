const validator = require('validator');
const xss = require('xss');
const securityConfig = require('../config/security');

/**
 * Enhanced validation utilities with security focus
 */
class SecureValidation {
  /**
   * Sanitize and validate email
   */
  static validateEmail(email) {
    if (!email || typeof email !== 'string') {
      return { isValid: false, error: 'Email is required' };
    }

    // Sanitize
    const sanitized = validator.escape(email.toLowerCase().trim());
    
    // Validate format
    if (!validator.isEmail(sanitized)) {
      return { isValid: false, error: 'Invalid email format' };
    }

    // Check length
    if (sanitized.length > 254) {
      return { isValid: false, error: 'Email is too long' };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /[<>]/,  // HTML tags
      /javascript:/i,  // JavaScript protocol
      /data:/i,  // Data protocol
      /vbscript:/i,  // VBScript protocol
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sanitized)) {
        return { isValid: false, error: 'Email contains invalid characters' };
      }
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Sanitize and validate name fields
   */
  static validateName(name, fieldName = 'Name') {
    if (!name || typeof name !== 'string') {
      return { isValid: false, error: `${fieldName} is required` };
    }

    // Sanitize
    let sanitized = validator.escape(name.trim());
    sanitized = xss(sanitized);

    // Validate length
    if (sanitized.length < 1) {
      return { isValid: false, error: `${fieldName} cannot be empty` };
    }

    if (sanitized.length > 50) {
      return { isValid: false, error: `${fieldName} is too long (max 50 characters)` };
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!/^[a-zA-Z\s\-']+$/.test(sanitized)) {
      return { isValid: false, error: `${fieldName} contains invalid characters` };
    }

    // Check for suspicious patterns
    if (/(.)\1{4,}/.test(sanitized)) {
      return { isValid: false, error: `${fieldName} contains suspicious patterns` };
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Sanitize and validate phone number
   */
  static validatePhone(phone) {
    if (!phone) {
      return { isValid: true, value: null }; // Phone is optional
    }

    if (typeof phone !== 'string') {
      return { isValid: false, error: 'Invalid phone number format' };
    }

    // Remove all non-digit characters except + at the beginning
    let sanitized = phone.replace(/[^\d+]/g, '');
    
    // Ensure + is only at the beginning
    if (sanitized.includes('+') && !sanitized.startsWith('+')) {
      return { isValid: false, error: 'Invalid phone number format' };
    }

    // Validate using validator.js
    if (!validator.isMobilePhone(sanitized, 'any', { strictMode: false })) {
      return { isValid: false, error: 'Invalid phone number' };
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Validate UUID
   */
  static validateUUID(id, fieldName = 'ID') {
    if (!id || typeof id !== 'string') {
      return { isValid: false, error: `${fieldName} is required` };
    }

    if (!validator.isUUID(id)) {
      return { isValid: false, error: `Invalid ${fieldName} format` };
    }

    return { isValid: true, value: id };
  }

  /**
   * Sanitize and validate text content
   */
  static validateText(text, options = {}) {
    const {
      fieldName = 'Text',
      required = false,
      minLength = 0,
      maxLength = 1000,
      allowHtml = false
    } = options;

    if (!text || typeof text !== 'string') {
      if (required) {
        return { isValid: false, error: `${fieldName} is required` };
      }
      return { isValid: true, value: null };
    }

    // Sanitize
    let sanitized = text.trim();
    
    if (!allowHtml) {
      sanitized = validator.escape(sanitized);
      sanitized = xss(sanitized);
    } else {
      // If HTML is allowed, use XSS protection with whitelist
      sanitized = xss(sanitized, {
        whiteList: {
          p: [],
          br: [],
          strong: [],
          em: [],
          u: [],
          ol: [],
          ul: [],
          li: []
        }
      });
    }

    // Validate length
    if (sanitized.length < minLength) {
      return { isValid: false, error: `${fieldName} must be at least ${minLength} characters` };
    }

    if (sanitized.length > maxLength) {
      return { isValid: false, error: `${fieldName} must not exceed ${maxLength} characters` };
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Validate numeric values
   */
  static validateNumber(value, options = {}) {
    const {
      fieldName = 'Number',
      required = false,
      min = Number.MIN_SAFE_INTEGER,
      max = Number.MAX_SAFE_INTEGER,
      integer = false
    } = options;

    if (value === null || value === undefined || value === '') {
      if (required) {
        return { isValid: false, error: `${fieldName} is required` };
      }
      return { isValid: true, value: null };
    }

    const num = Number(value);

    if (isNaN(num)) {
      return { isValid: false, error: `${fieldName} must be a valid number` };
    }

    if (integer && !Number.isInteger(num)) {
      return { isValid: false, error: `${fieldName} must be an integer` };
    }

    if (num < min) {
      return { isValid: false, error: `${fieldName} must be at least ${min}` };
    }

    if (num > max) {
      return { isValid: false, error: `${fieldName} must not exceed ${max}` };
    }

    return { isValid: true, value: num };
  }

  /**
   * Validate URL
   */
  static validateUrl(url, fieldName = 'URL') {
    if (!url || typeof url !== 'string') {
      return { isValid: false, error: `${fieldName} is required` };
    }

    const sanitized = url.trim();

    if (!validator.isURL(sanitized, {
      protocols: ['http', 'https'],
      require_protocol: true,
      require_valid_protocol: true,
      allow_underscores: false
    })) {
      return { isValid: false, error: `Invalid ${fieldName} format` };
    }

    // Additional security checks
    const suspiciousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /file:/i,
      /ftp:/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sanitized)) {
        return { isValid: false, error: `${fieldName} contains invalid protocol` };
      }
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Validate file upload
   */
  static validateFile(file, options = {}) {
    const config = securityConfig.getConfig();
    const {
      fieldName = 'File',
      required = false,
      maxSize = config.upload.maxFileSize,
      allowedTypes = config.upload.allowedMimeTypes
    } = options;

    if (!file) {
      if (required) {
        return { isValid: false, error: `${fieldName} is required` };
      }
      return { isValid: true, value: null };
    }

    // Check file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return { isValid: false, error: `${fieldName} size must not exceed ${maxSizeMB}MB` };
    }

    // Check MIME type
    if (!allowedTypes.includes(file.mimetype)) {
      return { isValid: false, error: `${fieldName} type not allowed. Allowed types: ${allowedTypes.join(', ')}` };
    }

    // Check file extension matches MIME type
    const extension = file.originalname.split('.').pop().toLowerCase();
    const mimeToExt = {
      'image/jpeg': ['jpg', 'jpeg'],
      'image/png': ['png'],
      'image/gif': ['gif'],
      'image/webp': ['webp'],
      'application/pdf': ['pdf']
    };

    const expectedExtensions = mimeToExt[file.mimetype];
    if (expectedExtensions && !expectedExtensions.includes(extension)) {
      return { isValid: false, error: `${fieldName} extension does not match file type` };
    }

    return { isValid: true, value: file };
  }

  /**
   * Validate and sanitize search query
   */
  static validateSearchQuery(query) {
    if (!query || typeof query !== 'string') {
      return { isValid: true, value: '' };
    }

    // Sanitize
    let sanitized = query.trim();
    sanitized = validator.escape(sanitized);
    sanitized = xss(sanitized);

    // Remove SQL injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi
    ];

    for (const pattern of sqlPatterns) {
      sanitized = sanitized.replace(pattern, '');
    }

    // Limit length
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }

    return { isValid: true, value: sanitized };
  }

  /**
   * Batch validation
   */
  static validateBatch(validations) {
    const errors = [];
    const values = {};

    for (const [field, validation] of Object.entries(validations)) {
      if (!validation.isValid) {
        errors.push({ field, message: validation.error });
      } else {
        values[field] = validation.value;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      values
    };
  }
}

module.exports = SecureValidation;

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, PaginationMeta } from '../../types';
import { apiHelpers } from '../../config/api';
import toast from 'react-hot-toast';

interface WishlistItem {
  id: string;
  userId: string;
  productId: string;
  product: Product;
  createdAt: string;
}

interface WishlistState {
  items: WishlistItem[];
  pagination: PaginationMeta | null;
  isLoading: boolean;
  error: string | null;
  wishlistProductIds: Set<string>;
}

const initialState: WishlistState = {
  items: [],
  pagination: null,
  isLoading: false,
  error: null,
  wishlistProductIds: new Set(),
};

// Async thunks
export const fetchWishlist = createAsyncThunk(
  'wishlist/fetchWishlist',
  async (params: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const { page = 1, limit = 20 } = params;
      const response: any = await apiHelpers.get(`/wishlist?page=${page}&limit=${limit}`);
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch wishlist');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch wishlist');
    }
  }
);

export const addToWishlist = createAsyncThunk(
  'wishlist/addToWishlist',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.post('/wishlist', { productId });
      if (response.success) {
        toast.success('Added to wishlist!');
        return { productId, wishlistItem: response.data.wishlistItem };
      }
      return rejectWithValue(response.error?.message || 'Failed to add to wishlist');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to add to wishlist';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const removeFromWishlist = createAsyncThunk(
  'wishlist/removeFromWishlist',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.delete(`/wishlist/${productId}`);
      if (response.success) {
        toast.success('Removed from wishlist');
        return productId;
      }
      return rejectWithValue(response.error?.message || 'Failed to remove from wishlist');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to remove from wishlist';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const checkWishlistStatus = createAsyncThunk(
  'wishlist/checkWishlistStatus',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.get(`/wishlist/${productId}/status`);
      if (response.success) {
        return { productId, inWishlist: response.data.inWishlist };
      }
      return rejectWithValue(response.error?.message || 'Failed to check wishlist status');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to check wishlist status');
    }
  }
);

export const clearWishlist = createAsyncThunk(
  'wishlist/clearWishlist',
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.delete('/wishlist/clear');
      if (response.success) {
        toast.success('Wishlist cleared');
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to clear wishlist');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to clear wishlist';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    toggleWishlistItem: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (state.wishlistProductIds.has(productId)) {
        state.wishlistProductIds.delete(productId);
      } else {
        state.wishlistProductIds.add(productId);
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch wishlist
    builder
      .addCase(fetchWishlist.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWishlist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.wishlist;
        state.pagination = action.payload.pagination;
        state.wishlistProductIds = new Set(action.payload.wishlist.map((item: WishlistItem) => item.productId));
        state.error = null;
      })
      .addCase(fetchWishlist.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Add to wishlist
    builder
      .addCase(addToWishlist.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addToWishlist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wishlistProductIds.add(action.payload.productId);
        state.items.unshift(action.payload.wishlistItem);
        state.error = null;
      })
      .addCase(addToWishlist.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Remove from wishlist
    builder
      .addCase(removeFromWishlist.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(removeFromWishlist.fulfilled, (state, action) => {
        state.isLoading = false;
        const productId = action.payload;
        state.wishlistProductIds.delete(productId);
        state.items = state.items.filter(item => item.productId !== productId);
        state.error = null;
      })
      .addCase(removeFromWishlist.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Check wishlist status
    builder
      .addCase(checkWishlistStatus.fulfilled, (state, action) => {
        const { productId, inWishlist } = action.payload;
        if (inWishlist) {
          state.wishlistProductIds.add(productId);
        } else {
          state.wishlistProductIds.delete(productId);
        }
      });

    // Clear wishlist
    builder
      .addCase(clearWishlist.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(clearWishlist.fulfilled, (state) => {
        state.isLoading = false;
        state.items = [];
        state.wishlistProductIds.clear();
        state.pagination = null;
        state.error = null;
      })
      .addCase(clearWishlist.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, toggleWishlistItem } = wishlistSlice.actions;
export default wishlistSlice.reducer;

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { setFilters, fetchProducts, fetchCategories } from '../../store/slices/productSlice';
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ProductFiltersProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch();
  const { categories, filters } = useAppSelector((state) => state.products);
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Local state for filters
  const [localFilters, setLocalFilters] = useState({
    category: filters.category || '',
    minPrice: filters.minPrice || 0,
    maxPrice: filters.maxPrice || 1000,
    sortBy: filters.sortBy || 'createdAt',
    brands: [] as string[],
  });

  // Available brands (this would typically come from the backend)
  const availableBrands = [
    'Apple', 'Samsung', 'Google', 'Sony', 'Dell', 'Nike', 'Adidas', 
    'Zara', 'H&M', 'Ralph Lauren', 'Levi\'s', 'All-Clad', 'Herman Miller',
    'The Ordinary', 'Morphe', 'J.Crew', 'Cole Haan'
  ];

  const sortOptions = [
    { value: 'createdAt', label: 'Newest First' },
    { value: 'price_asc', label: 'Price: Low to High' },
    { value: 'price_desc', label: 'Price: High to Low' },
    { value: 'name', label: 'Name: A to Z' },
    { value: 'featured', label: 'Featured' },
    { value: 'relevance', label: 'Most Relevant' },
  ];

  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);

  const handleFilterChange = (key: string, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleBrandToggle = (brand: string) => {
    setLocalFilters(prev => ({
      ...prev,
      brands: prev.brands.includes(brand)
        ? prev.brands.filter(b => b !== brand)
        : [...prev.brands, brand]
    }));
  };

  const applyFilters = () => {
    const newFilters = {
      ...localFilters,
      page: 1, // Reset to first page when applying filters
    };

    // Update URL params
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            params.set(key, value.join(','));
          } else {
            params.delete(key);
          }
        } else {
          params.set(key, value.toString());
        }
      } else {
        params.delete(key);
      }
    });
    setSearchParams(params);

    // Update Redux state and fetch products
    dispatch(setFilters(newFilters));
    dispatch(fetchProducts(newFilters));
    onClose();
  };

  const clearFilters = () => {
    const clearedFilters = {
      category: '',
      minPrice: 0,
      maxPrice: 1000,
      sortBy: 'createdAt',
      brands: [],
    };
    
    setLocalFilters(clearedFilters);
    
    // Clear URL params
    setSearchParams({});
    
    // Update Redux state and fetch products
    dispatch(setFilters({ page: 1, limit: 20 }));
    dispatch(fetchProducts({ page: 1, limit: 20 }));
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose}></div>
      
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto px-6 py-4">
            {/* Sort By */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Sort By</h3>
              <select
                value={localFilters.sortBy}
                onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Category */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Category</h3>
              <select
                value={localFilters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Price Range */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Price Range</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min Price</label>
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    step="10"
                    value={localFilters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="text-sm text-gray-600">${localFilters.minPrice}</div>
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max Price</label>
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    step="10"
                    value={localFilters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="text-sm text-gray-600">${localFilters.maxPrice}</div>
                </div>
              </div>
            </div>

            {/* Brands */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Brands</h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {availableBrands.map((brand) => (
                  <label key={brand} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={localFilters.brands.includes(brand)}
                      onChange={() => handleBrandToggle(brand)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{brand}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 px-6 py-4">
            <div className="flex space-x-3">
              <button
                onClick={clearFilters}
                className="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Clear All
              </button>
              <button
                onClick={applyFilters}
                className="flex-1 rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;

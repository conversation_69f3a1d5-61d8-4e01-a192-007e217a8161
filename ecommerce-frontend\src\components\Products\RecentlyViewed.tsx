import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { loadRecentlyViewed } from '../../store/slices/recentlyViewedSlice';
import { addToCart, fetchCart } from '../../store/slices/cartSlice';
import { StarIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import WishlistButton from './WishlistButton';
import ComparisonButton from './ComparisonButton';

const RecentlyViewed: React.FC = () => {
  const dispatch = useAppDispatch();
  const { products } = useAppSelector((state) => state.recentlyViewed);
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  useEffect(() => {
    dispatch(loadRecentlyViewed());
  }, [dispatch]);

  const handleAddToCart = async (productId: string) => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    try {
      await dispatch(addToCart({ productId, quantity: 1 })).unwrap();
      dispatch(fetchCart());
    } catch (error) {
      // Error is handled by the slice and toast
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <StarIcon
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  if (products.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Recently Viewed</h2>
          <Link
            to="/products"
            className="text-primary-600 hover:text-primary-700 font-medium"
          >
            View All Products →
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.slice(0, 4).map((product) => (
            <div
              key={product.id}
              className="group bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
            >
              <div className="relative overflow-hidden">
                <Link to={`/products/${product.id}`}>
                  <img
                    src={product.images[0] || 'https://via.placeholder.com/300x200'}
                    alt={product.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </Link>
                <div className="absolute top-2 right-2 flex space-x-1">
                  <ComparisonButton product={product} size="sm" />
                  <WishlistButton productId={product.id} size="sm" />
                </div>
                {product.comparePrice && product.comparePrice > product.price && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                    {Math.round(((product.comparePrice - product.price) / product.comparePrice) * 100)}% OFF
                  </div>
                )}
              </div>
              
              <div className="p-4">
                <Link to={`/products/${product.id}`}>
                  <h3 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600">
                    {product.name}
                  </h3>
                </Link>
                
                {product.averageRating && product.averageRating > 0 && (
                  <div className="mb-2">
                    {renderStars(product.averageRating)}
                  </div>
                )}
                
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">
                      {formatPrice(product.price)}
                    </span>
                    {product.comparePrice && product.comparePrice > product.price && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(product.comparePrice)}
                      </span>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => handleAddToCart(product.id)}
                  disabled={product.quantity === 0}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ShoppingCartIcon className="h-4 w-4 mr-2" />
                  {product.quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {products.length > 4 && (
          <div className="text-center mt-8">
            <p className="text-gray-600">
              You have {products.length - 4} more recently viewed products.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default RecentlyViewed;

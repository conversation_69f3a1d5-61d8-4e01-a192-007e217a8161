#!/usr/bin/env node

/**
 * Security Testing Script
 * 
 * This script performs basic security tests on the e-commerce API
 * to validate security implementations.
 */

const axios = require('axios');
const crypto = require('crypto');

const API_BASE_URL = process.env.API_URL || 'http://localhost:3000/api/v1';

class SecurityTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(name, testFn) {
    console.log(`\n🧪 Testing: ${name}`);
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED' });
      console.log(`✅ PASSED: ${name}`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`❌ FAILED: ${name} - ${error.message}`);
    }
  }

  async testRateLimiting() {
    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.post(`${API_BASE_URL}/auth/login`, {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }, { timeout: 5000 })
        .catch(err => err.response)
      );
    }

    const responses = await Promise.all(requests);
    const rateLimitedResponses = responses.filter(res => res?.status === 429);
    
    if (rateLimitedResponses.length === 0) {
      throw new Error('Rate limiting not working - no 429 responses received');
    }
  }

  async testXSSProtection() {
    const xssPayloads = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src="x" onerror="alert(1)">',
      '"><script>alert("xss")</script>',
    ];

    for (const payload of xssPayloads) {
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/register`, {
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: payload,
          lastName: 'Test'
        }, { timeout: 5000 });

        // Check if the payload was sanitized
        if (response.data.data?.user?.firstName?.includes('<script>')) {
          throw new Error(`XSS payload not sanitized: ${payload}`);
        }
      } catch (error) {
        if (error.response?.status !== 400) {
          throw new Error(`Unexpected error for XSS test: ${error.message}`);
        }
      }
    }
  }

  async testSQLInjection() {
    const sqlPayloads = [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "admin'/*",
    ];

    for (const payload of sqlPayloads) {
      try {
        await axios.post(`${API_BASE_URL}/auth/login`, {
          email: payload,
          password: 'password'
        }, { timeout: 5000 });
      } catch (error) {
        // We expect these to fail, but not with database errors
        if (error.response?.data?.error?.message?.toLowerCase().includes('sql')) {
          throw new Error(`Possible SQL injection vulnerability: ${payload}`);
        }
      }
    }
  }

  async testPasswordPolicy() {
    const weakPasswords = [
      'password',
      '123456',
      'qwerty',
      'abc123',
      'password123',
      '12345678'
    ];

    for (const password of weakPasswords) {
      try {
        const response = await axios.post(`${API_BASE_URL}/auth/register`, {
          email: `test${Date.now()}@example.com`,
          password: password,
          firstName: 'Test',
          lastName: 'User'
        }, { timeout: 5000 });

        if (response.status === 201) {
          throw new Error(`Weak password accepted: ${password}`);
        }
      } catch (error) {
        if (error.response?.status !== 400) {
          throw new Error(`Unexpected error for password test: ${error.message}`);
        }
      }
    }
  }

  async testSecurityHeaders() {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
      
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'strict-transport-security'
      ];

      for (const header of requiredHeaders) {
        if (!response.headers[header]) {
          throw new Error(`Missing security header: ${header}`);
        }
      }
    } catch (error) {
      if (error.message.includes('Missing security header')) {
        throw error;
      }
      // If endpoint doesn't exist, that's okay for this test
    }
  }

  async testFileUploadSecurity() {
    // Test malicious file upload
    const maliciousFiles = [
      { name: 'test.exe', type: 'application/x-executable' },
      { name: 'test.php', type: 'application/x-php' },
      { name: 'test.js', type: 'application/javascript' },
      { name: '../../../etc/passwd', type: 'text/plain' },
    ];

    for (const file of maliciousFiles) {
      try {
        const formData = new FormData();
        const blob = new Blob(['malicious content'], { type: file.type });
        formData.append('file', blob, file.name);

        const response = await axios.post(`${API_BASE_URL}/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 5000
        });

        if (response.status === 200) {
          throw new Error(`Malicious file upload accepted: ${file.name}`);
        }
      } catch (error) {
        if (error.response?.status !== 400 && error.response?.status !== 415) {
          // 400 or 415 are expected for rejected files
          if (!error.message.includes('Malicious file upload accepted')) {
            // Endpoint might not exist, which is okay
            continue;
          }
          throw error;
        }
      }
    }
  }

  async testAccountLockout() {
    const testEmail = `lockout-test-${Date.now()}@example.com`;
    
    // First register a user
    try {
      await axios.post(`${API_BASE_URL}/auth/register`, {
        email: testEmail,
        password: 'ValidPassword123!',
        firstName: 'Test',
        lastName: 'User'
      }, { timeout: 5000 });
    } catch (error) {
      // User might already exist, continue with test
    }

    // Try multiple failed logins
    const failedAttempts = [];
    for (let i = 0; i < 6; i++) {
      try {
        await axios.post(`${API_BASE_URL}/auth/login`, {
          email: testEmail,
          password: 'wrongpassword'
        }, { timeout: 5000 });
      } catch (error) {
        failedAttempts.push(error.response?.status);
      }
    }

    // Check if account got locked (should get 423 status)
    const lockedResponses = failedAttempts.filter(status => status === 423);
    if (lockedResponses.length === 0) {
      throw new Error('Account lockout not working - no 423 responses received');
    }
  }

  async testCSRFProtection() {
    // Test if API accepts requests without proper CSRF tokens
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password'
      }, {
        headers: {
          'Origin': 'http://malicious-site.com',
          'Referer': 'http://malicious-site.com'
        },
        timeout: 5000
      });

      // If CORS is properly configured, this should fail
      if (response.status === 200) {
        console.warn('⚠️  WARNING: Possible CSRF vulnerability - check CORS configuration');
      }
    } catch (error) {
      // CORS error is expected and good
      if (error.code === 'ERR_NETWORK' || error.message.includes('CORS')) {
        return; // This is good - CORS is working
      }
      throw error;
    }
  }

  async testInputValidation() {
    const invalidInputs = [
      { field: 'email', value: 'not-an-email' },
      { field: 'email', value: 'a'.repeat(300) + '@example.com' },
      { field: 'firstName', value: 'a'.repeat(100) },
      { field: 'phone', value: 'not-a-phone' },
    ];

    for (const input of invalidInputs) {
      try {
        const data = {
          email: '<EMAIL>',
          password: 'ValidPassword123!',
          firstName: 'Valid',
          lastName: 'User'
        };
        data[input.field] = input.value;

        const response = await axios.post(`${API_BASE_URL}/auth/register`, data, { timeout: 5000 });
        
        if (response.status === 201) {
          throw new Error(`Invalid input accepted for ${input.field}: ${input.value}`);
        }
      } catch (error) {
        if (error.response?.status !== 400) {
          if (!error.message.includes('Invalid input accepted')) {
            continue; // Other errors are okay
          }
          throw error;
        }
      }
    }
  }

  async runAllTests() {
    console.log('🔒 Starting Security Tests...\n');
    console.log(`Testing API at: ${API_BASE_URL}`);

    await this.runTest('Rate Limiting', () => this.testRateLimiting());
    await this.runTest('XSS Protection', () => this.testXSSProtection());
    await this.runTest('SQL Injection Protection', () => this.testSQLInjection());
    await this.runTest('Password Policy', () => this.testPasswordPolicy());
    await this.runTest('Security Headers', () => this.testSecurityHeaders());
    await this.runTest('File Upload Security', () => this.testFileUploadSecurity());
    await this.runTest('Account Lockout', () => this.testAccountLockout());
    await this.runTest('CSRF Protection', () => this.testCSRFProtection());
    await this.runTest('Input Validation', () => this.testInputValidation());

    this.printResults();
  }

  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('🔒 SECURITY TEST RESULTS');
    console.log('='.repeat(50));
    
    console.log(`\n📊 Summary:`);
    console.log(`   ✅ Passed: ${this.results.passed}`);
    console.log(`   ❌ Failed: ${this.results.failed}`);
    console.log(`   📈 Success Rate: ${Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100)}%`);

    if (this.results.failed > 0) {
      console.log(`\n❌ Failed Tests:`);
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }

    console.log('\n' + '='.repeat(50));
    
    if (this.results.failed === 0) {
      console.log('🎉 All security tests passed!');
    } else {
      console.log('⚠️  Some security tests failed. Please review and fix the issues.');
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Security test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = SecurityTester;

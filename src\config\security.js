const crypto = require('crypto');
const logger = require('../utils/logger');

/**
 * Security Configuration and Environment Variable Validation
 */
class SecurityConfig {
  constructor() {
    this.validateEnvironment();
    this.initializeSecuritySettings();
  }

  /**
   * Validate all required environment variables
   */
  validateEnvironment() {
    const requiredVars = [
      'NODE_ENV',
      'PORT',
      'DATABASE_URL',
      'JWT_SECRET',
      'JWT_REFRESH_SECRET',
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      logger.error(`Missing required environment variables: ${missingVars.join(', ')}`);
      process.exit(1);
    }

    // Validate JWT secrets strength in production
    if (process.env.NODE_ENV === 'production') {
      this.validateProductionSecrets();
    }

    // Validate database URL format
    if (!this.isValidDatabaseUrl(process.env.DATABASE_URL)) {
      logger.error('Invalid DATABASE_URL format');
      process.exit(1);
    }

    logger.info('Environment variables validation passed');
  }

  /**
   * Validate production secrets strength
   */
  validateProductionSecrets() {
    const secrets = [
      { name: 'JWT_SECRET', value: process.env.JWT_SECRET },
      { name: 'JWT_REFRESH_SECRET', value: process.env.JWT_REFRESH_SECRET }
    ];

    secrets.forEach(({ name, value }) => {
      if (value.length < 32) {
        logger.error(`${name} must be at least 32 characters long in production`);
        process.exit(1);
      }

      if (value.includes('dev-') || value.includes('test-') || value.includes('change-in-production')) {
        logger.error(`${name} appears to be a development secret. Use a secure secret in production`);
        process.exit(1);
      }
    });
  }

  /**
   * Validate database URL format
   */
  isValidDatabaseUrl(url) {
    try {
      const parsed = new URL(url);
      return ['postgresql:', 'postgres:', 'mysql:', 'sqlite:'].includes(parsed.protocol);
    } catch (error) {
      return false;
    }
  }

  /**
   * Initialize security settings
   */
  initializeSecuritySettings() {
    this.config = {
      // JWT Configuration
      jwt: {
        secret: process.env.JWT_SECRET,
        refreshSecret: process.env.JWT_REFRESH_SECRET,
        expiresIn: process.env.JWT_EXPIRE || '15m',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRE || '7d',
        algorithm: 'HS256',
        issuer: 'ecommerce-api',
        audience: 'ecommerce-client'
      },

      // Password Policy
      password: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        bcryptRounds: process.env.NODE_ENV === 'production' ? 14 : 12
      },

      // Rate Limiting
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
        authWindowMs: 15 * 60 * 1000, // 15 minutes for auth endpoints
        authMaxRequests: 5, // 5 login attempts per 15 minutes
        skipSuccessfulRequests: true,
        skipFailedRequests: false
      },

      // CORS Configuration
      cors: {
        origin: this.parseCorsOrigins(),
        credentials: true,
        optionsSuccessStatus: 200,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: [
          'Origin',
          'X-Requested-With',
          'Content-Type',
          'Accept',
          'Authorization',
          'X-API-Key',
          'X-CSRF-Token'
        ]
      },

      // File Upload Security
      upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB
        allowedMimeTypes: [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'application/pdf'
        ],
        uploadPath: process.env.UPLOAD_PATH || 'uploads/',
        maxFiles: 10
      },

      // Session Security
      session: {
        name: 'ecommerce.sid',
        secret: this.generateSessionSecret(),
        resave: false,
        saveUninitialized: false,
        cookie: {
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          sameSite: 'strict'
        }
      },

      // Security Headers
      security: {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"]
          }
        },
        hsts: {
          maxAge: 31536000, // 1 year
          includeSubDomains: true,
          preload: true
        }
      },

      // Audit Logging
      audit: {
        enabled: true,
        sensitiveOperations: [
          'user.login',
          'user.logout',
          'user.register',
          'user.password_change',
          'user.password_reset',
          'user.profile_update',
          'order.create',
          'order.update',
          'payment.process',
          'admin.action'
        ]
      },

      // Data Encryption
      encryption: {
        algorithm: 'aes-256-gcm',
        keyLength: 32,
        ivLength: 16,
        tagLength: 16,
        key: this.generateEncryptionKey()
      }
    };
  }

  /**
   * Parse CORS origins from environment variable
   */
  parseCorsOrigins() {
    const origins = process.env.CORS_ORIGIN;
    if (!origins) {
      return process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000', 'http://localhost:3001'];
    }
    return origins.split(',').map(origin => origin.trim());
  }

  /**
   * Generate session secret
   */
  generateSessionSecret() {
    return process.env.SESSION_SECRET || crypto.randomBytes(64).toString('hex');
  }

  /**
   * Generate encryption key
   */
  generateEncryptionKey() {
    const key = process.env.ENCRYPTION_KEY;
    if (key) {
      return Buffer.from(key, 'hex');
    }
    
    // Generate a new key if not provided (for development only)
    if (process.env.NODE_ENV !== 'production') {
      const newKey = crypto.randomBytes(32);
      logger.warn('Generated new encryption key for development. Set ENCRYPTION_KEY in production.');
      return newKey;
    }
    
    logger.error('ENCRYPTION_KEY is required in production');
    process.exit(1);
  }

  /**
   * Get configuration
   */
  getConfig() {
    return this.config;
  }

  /**
   * Sanitize sensitive data from logs
   */
  sanitizeForLogging(data) {
    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'cookie',
      'session',
      'credit_card',
      'ssn',
      'email'
    ];

    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sanitized = { ...data };
    
    Object.keys(sanitized).forEach(key => {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    });

    return sanitized;
  }
}

// Export singleton instance
const securityConfig = new SecurityConfig();
module.exports = securityConfig;

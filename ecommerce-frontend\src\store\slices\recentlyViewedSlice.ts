import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Product } from '../../types';

interface RecentlyViewedState {
  products: Product[];
  maxProducts: number;
}

const initialState: RecentlyViewedState = {
  products: [],
  maxProducts: 10, // Maximum number of recently viewed products to store
};

// Load from localStorage on initialization
const loadFromLocalStorage = (): Product[] => {
  try {
    const stored = localStorage.getItem('recentlyViewed');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading recently viewed products from localStorage:', error);
    return [];
  }
};

// Save to localStorage
const saveToLocalStorage = (products: Product[]) => {
  try {
    localStorage.setItem('recentlyViewed', JSON.stringify(products));
  } catch (error) {
    console.error('Error saving recently viewed products to localStorage:', error);
  }
};

const recentlyViewedSlice = createSlice({
  name: 'recentlyViewed',
  initialState: {
    ...initialState,
    products: loadFromLocalStorage(),
  },
  reducers: {
    addToRecentlyViewed: (state, action: PayloadAction<Product>) => {
      const product = action.payload;
      
      // Remove the product if it already exists
      state.products = state.products.filter(p => p.id !== product.id);
      
      // Add the product to the beginning of the array
      state.products.unshift(product);
      
      // Keep only the maximum number of products
      if (state.products.length > state.maxProducts) {
        state.products = state.products.slice(0, state.maxProducts);
      }
      
      // Save to localStorage
      saveToLocalStorage(state.products);
    },

    removeFromRecentlyViewed: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      state.products = state.products.filter(p => p.id !== productId);
      saveToLocalStorage(state.products);
    },

    clearRecentlyViewed: (state) => {
      state.products = [];
      saveToLocalStorage(state.products);
    },

    loadRecentlyViewed: (state) => {
      state.products = loadFromLocalStorage();
    },
  },
});

export const {
  addToRecentlyViewed,
  removeFromRecentlyViewed,
  clearRecentlyViewed,
  loadRecentlyViewed,
} = recentlyViewedSlice.actions;

export default recentlyViewedSlice.reducer;

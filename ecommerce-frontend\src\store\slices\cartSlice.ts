import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CartItem, CartSummary } from '../../types';
import { apiHelpers } from '../../config/api';
import toast from 'react-hot-toast';

interface CartState {
  items: CartItem[];
  summary: CartSummary;
  isLoading: boolean;
  error: string | null;
}

const initialState: CartState = {
  items: [],
  summary: {
    totalItems: 0,
    subtotal: 0,
    itemCount: 0,
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.get('/cart');
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to fetch cart');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to fetch cart');
    }
  }
);

export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async (
    { productId, variantId, quantity }: { productId: string; variantId?: string; quantity: number },
    { rejectWithValue }
  ) => {
    try {
      const response: any = await apiHelpers.post('/cart', {
        productId,
        variantId,
        quantity,
      });
      if (response.success) {
        toast.success('Item added to cart!');
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to add item to cart');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async (
    { cartItemId, quantity }: { cartItemId: string; quantity: number },
    { rejectWithValue }
  ) => {
    try {
      const response: any = await apiHelpers.put(`/cart/${cartItemId}`, { quantity });
      if (response.success) {
        toast.success('Cart updated!');
        return response.data;
      }
      return rejectWithValue(response.error?.message || 'Failed to update cart item');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async (cartItemId: string, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.delete(`/cart/${cartItemId}`);
      if (response.success) {
        toast.success('Item removed from cart!');
        return cartItemId;
      }
      return rejectWithValue(response.error?.message || 'Failed to remove item from cart');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await apiHelpers.delete('/cart');
      if (response.success) {
        toast.success('Cart cleared!');
        return null;
      }
      return rejectWithValue(response.error?.message || 'Failed to clear cart');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to clear cart');
    }
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateLocalCartItem: (state, action: PayloadAction<{ id: string; quantity: number }>) => {
      const { id, quantity } = action.payload;
      const item = state.items.find(item => item.id === id);
      if (item) {
        item.quantity = quantity;
        item.totalPrice = item.unitPrice * quantity;
        // Recalculate summary
        state.summary.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
        state.summary.subtotal = state.items.reduce((total, item) => total + item.totalPrice, 0);
      }
    },
    removeLocalCartItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      // Recalculate summary
      state.summary.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.summary.subtotal = state.items.reduce((total, item) => total + item.totalPrice, 0);
      state.summary.itemCount = state.items.length;
    },
  },
  extraReducers: (builder) => {
    // Fetch cart
    builder
      .addCase(fetchCart.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.cartItems;
        state.summary = action.payload.summary;
        state.error = null;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Add to cart
    builder
      .addCase(addToCart.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.isLoading = false;
        // Refresh cart after adding item
        // This will be handled by refetching the cart
        state.error = null;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update cart item
    builder
      .addCase(updateCartItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.isLoading = false;
        const updatedItem = action.payload.cartItem;
        const index = state.items.findIndex(item => item.id === updatedItem.id);
        if (index !== -1) {
          state.items[index] = updatedItem;
          // Recalculate summary
          state.summary.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
          state.summary.subtotal = state.items.reduce((total, item) => total + item.totalPrice, 0);
        }
        state.error = null;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Remove from cart
    builder
      .addCase(removeFromCart.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = state.items.filter(item => item.id !== action.payload);
        // Recalculate summary
        state.summary.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
        state.summary.subtotal = state.items.reduce((total, item) => total + item.totalPrice, 0);
        state.summary.itemCount = state.items.length;
        state.error = null;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Clear cart
    builder
      .addCase(clearCart.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.isLoading = false;
        state.items = [];
        state.summary = {
          totalItems: 0,
          subtotal: 0,
          itemCount: 0,
        };
        state.error = null;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, updateLocalCartItem, removeLocalCartItem } = cartSlice.actions;
export default cartSlice.reducer;

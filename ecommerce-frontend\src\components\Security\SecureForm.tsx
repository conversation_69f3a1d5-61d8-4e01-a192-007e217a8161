import React, { useState, useEffect } from 'react';
import { InputSanitizer, ClientRateLimit } from '../../config/security';
import { EyeIcon, EyeSlashIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';

interface SecureInputProps {
  type: 'text' | 'email' | 'password' | 'tel' | 'url' | 'search';
  name: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  maxLength?: number;
  pattern?: string;
  autoComplete?: string;
  showPasswordStrength?: boolean;
  onValidation?: (isValid: boolean, errors: string[]) => void;
}

export const SecureInput: React.FC<SecureInputProps> = ({
  type,
  name,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  maxLength,
  pattern,
  autoComplete,
  showPasswordStrength = false,
  onValidation,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Validate input on change
  useEffect(() => {
    const validationErrors: string[] = [];
    let isValid = true;

    // Required field validation
    if (required && !value.trim()) {
      validationErrors.push(`${name} is required`);
      isValid = false;
    }

    // Type-specific validation
    if (value.trim()) {
      switch (type) {
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            validationErrors.push('Please enter a valid email address');
            isValid = false;
          }
          break;

        case 'password':
          if (showPasswordStrength) {
            const strength = calculatePasswordStrength(value);
            setPasswordStrength(strength);
            
            if (strength < 30) {
              validationErrors.push('Password is too weak');
              isValid = false;
            }
          }
          break;

        case 'tel':
          const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
          if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            validationErrors.push('Please enter a valid phone number');
            isValid = false;
          }
          break;

        case 'url':
          try {
            new URL(value);
          } catch {
            validationErrors.push('Please enter a valid URL');
            isValid = false;
          }
          break;
      }
    }

    // Pattern validation
    if (pattern && value && !new RegExp(pattern).test(value)) {
      validationErrors.push('Input format is invalid');
      isValid = false;
    }

    // Length validation
    if (maxLength && value.length > maxLength) {
      validationErrors.push(`Maximum ${maxLength} characters allowed`);
      isValid = false;
    }

    setErrors(validationErrors);
    onValidation?.(isValid, validationErrors);
  }, [value, required, type, pattern, maxLength, name, showPasswordStrength, onValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let sanitizedValue = e.target.value;

    // Sanitize input based on type
    switch (type) {
      case 'email':
        sanitizedValue = InputSanitizer.sanitizeEmail(sanitizedValue);
        break;
      case 'search':
        sanitizedValue = InputSanitizer.sanitizeSearchQuery(sanitizedValue);
        break;
      case 'url':
        sanitizedValue = InputSanitizer.sanitizeUrl(sanitizedValue);
        break;
      default:
        sanitizedValue = InputSanitizer.sanitizeHtml(sanitizedValue);
    }

    onChange(sanitizedValue);
  };

  const calculatePasswordStrength = (password: string): number => {
    let score = 0;
    
    // Length bonus
    score += Math.min(password.length * 4, 25);
    
    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/\d/.test(password)) score += 5;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    
    // Unique characters bonus
    const uniqueChars = new Set(password).size;
    score += Math.min(uniqueChars * 2, 20);
    
    // Pattern penalties
    if (/(.)\1{2,}/.test(password)) score -= 10;
    if (/^(.{1,3})\1+$/.test(password)) score -= 20;
    
    return Math.max(0, Math.min(100, score));
  };

  const getPasswordStrengthLabel = (score: number): string => {
    if (score < 30) return 'Very Weak';
    if (score < 50) return 'Weak';
    if (score < 70) return 'Fair';
    if (score < 85) return 'Good';
    return 'Strong';
  };

  const getPasswordStrengthColor = (score: number): string => {
    if (score < 30) return 'bg-red-500';
    if (score < 50) return 'bg-orange-500';
    if (score < 70) return 'bg-yellow-500';
    if (score < 85) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="space-y-2">
      <div className="relative">
        <input
          type={inputType}
          name={name}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          maxLength={maxLength}
          pattern={pattern}
          autoComplete={autoComplete}
          className={`
            block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
            placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500
            ${errors.length > 0 ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
            ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
            ${className}
          `}
        />
        
        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            {showPassword ? (
              <EyeSlashIcon className="h-5 w-5 text-gray-400" />
            ) : (
              <EyeIcon className="h-5 w-5 text-gray-400" />
            )}
          </button>
        )}
      </div>

      {/* Password Strength Indicator */}
      {type === 'password' && showPasswordStrength && value && (
        <div className="space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Password Strength:</span>
            <span className={`font-medium ${
              passwordStrength < 30 ? 'text-red-600' :
              passwordStrength < 50 ? 'text-orange-600' :
              passwordStrength < 70 ? 'text-yellow-600' :
              passwordStrength < 85 ? 'text-blue-600' : 'text-green-600'
            }`}>
              {getPasswordStrengthLabel(passwordStrength)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength)}`}
              style={{ width: `${passwordStrength}%` }}
            />
          </div>
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="space-y-1">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-center">
              <span className="mr-1">⚠️</span>
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

interface SecureFormProps {
  onSubmit: (data: Record<string, any>) => Promise<void>;
  children: React.ReactNode;
  className?: string;
  rateLimitKey?: string;
  maxSubmissions?: number;
  submissionWindowMs?: number;
}

export const SecureForm: React.FC<SecureFormProps> = ({
  onSubmit,
  children,
  className = '',
  rateLimitKey = 'form_submission',
  maxSubmissions = 5,
  submissionWindowMs = 60000,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [csrfToken, setCsrfToken] = useState('');

  useEffect(() => {
    // Generate CSRF token
    setCsrfToken(Math.random().toString(36).substring(2, 15));
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Rate limiting check
    if (ClientRateLimit.isLimited(rateLimitKey, maxSubmissions, submissionWindowMs)) {
      alert('Too many submissions. Please wait before trying again.');
      return;
    }

    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      const formData = new FormData(e.currentTarget);
      const data: Record<string, any> = {};
      
      // Convert FormData to object and sanitize
      formData.forEach((value, key) => {
        if (typeof value === 'string') {
          data[key] = InputSanitizer.sanitizeHtml(value);
        } else {
          data[key] = value;
        }
      });

      // Add CSRF token
      data._csrf = csrfToken;

      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={className} noValidate>
      <input type="hidden" name="_csrf" value={csrfToken} />
      {children}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            <span className="text-gray-600">Submitting...</span>
          </div>
        </div>
      )}
    </form>
  );
};

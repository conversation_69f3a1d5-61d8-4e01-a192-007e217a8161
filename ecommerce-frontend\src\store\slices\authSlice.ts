import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService } from '../../services/authService';
import { tokenManager } from '../../config/api';
import { User, LoginRequest, RegisterRequest, AuthTokens } from '../../types';
import toast from 'react-hot-toast';

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  tokens: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials);
      if (response.success) {
        const { user, tokens } = response.data;
        tokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
        toast.success('Login successful!');
        return { user, tokens };
      }
      return rejectWithValue(response.error?.message || 'Login failed');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData: RegisterRequest, { rejectWithValue }) => {
    try {
      const response = await authService.register(userData);
      if (response.success) {
        const { user, tokens } = response.data;
        tokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
        toast.success('Registration successful!');
        return { user, tokens };
      }
      return rejectWithValue(response.error?.message || 'Registration failed');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Registration failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { getState, rejectWithValue }) => {
    try {
      const refreshToken = tokenManager.getRefreshToken();
      if (refreshToken) {
        await authService.logout(refreshToken);
      }
      tokenManager.clearTokens();
      toast.success('Logged out successfully');
      return null;
    } catch (error: any) {
      // Even if logout fails on server, clear local tokens
      tokenManager.clearTokens();
      return null;
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const token = tokenManager.getAccessToken();
      if (!token) {
        return rejectWithValue('No token found');
      }
      
      const response = await authService.getProfile();
      if (response.success) {
        return response.data.user;
      }
      return rejectWithValue(response.error?.message || 'Failed to get user');
    } catch (error: any) {
      tokenManager.clearTokens();
      return rejectWithValue(error.response?.data?.error?.message || 'Failed to get user');
    }
  }
);

export const verifyEmail = createAsyncThunk(
  'auth/verifyEmail',
  async (token: string, { rejectWithValue }) => {
    try {
      const response = await authService.verifyEmail(token);
      if (response.success) {
        toast.success('Email verified successfully!');
        return response.data.message;
      }
      return rejectWithValue(response.error?.message || 'Email verification failed');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Email verification failed');
    }
  }
);

export const requestPasswordReset = createAsyncThunk(
  'auth/requestPasswordReset',
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await authService.requestPasswordReset(email);
      if (response.success) {
        toast.success('Password reset email sent!');
        return response.data.message;
      }
      return rejectWithValue(response.error?.message || 'Password reset request failed');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Password reset request failed');
    }
  }
);

export const confirmPasswordReset = createAsyncThunk(
  'auth/confirmPasswordReset',
  async ({ token, password }: { token: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authService.confirmPasswordReset(token, password);
      if (response.success) {
        toast.success('Password reset successful!');
        return response.data.message;
      }
      return rejectWithValue(response.error?.message || 'Password reset failed');
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error?.message || 'Password reset failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    clearAuth: (state) => {
      state.user = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.error = null;
      tokenManager.clearTokens();
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.tokens = action.payload.tokens;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Register
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.tokens = action.payload.tokens;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Logout
    builder
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.tokens = null;
        state.isAuthenticated = false;
        state.error = null;
      });

    // Get current user
    builder
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setUser, clearAuth, updateUser } = authSlice.actions;
export default authSlice.reducer;

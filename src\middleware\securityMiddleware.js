const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const cors = require('cors');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const validator = require('validator');
const securityConfig = require('../config/security');
const logger = require('../utils/logger');
const { prisma } = require('../config/database');

const config = securityConfig.getConfig();

/**
 * Security Headers Middleware
 */
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: config.security.contentSecurityPolicy.directives,
    reportOnly: process.env.NODE_ENV !== 'production'
  },
  hsts: {
    maxAge: config.security.hsts.maxAge,
    includeSubDomains: config.security.hsts.includeSubDomains,
    preload: config.security.hsts.preload
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'same-origin' },
  frameguard: { action: 'deny' },
  hidePoweredBy: true
});

/**
 * CORS Middleware
 */
const corsMiddleware = cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (config.cors.origin === false) {
      return callback(new Error('Not allowed by CORS'), false);
    }
    
    if (Array.isArray(config.cors.origin)) {
      if (config.cors.origin.includes(origin)) {
        return callback(null, true);
      }
      return callback(new Error('Not allowed by CORS'), false);
    }
    
    callback(null, true);
  },
  credentials: config.cors.credentials,
  methods: config.cors.methods,
  allowedHeaders: config.cors.allowedHeaders,
  optionsSuccessStatus: config.cors.optionsSuccessStatus
});

/**
 * General Rate Limiting
 */
const generalRateLimit = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    success: false,
    error: {
      message: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/api/health';
  },
  onLimitReached: (req) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
  }
});

/**
 * Authentication Rate Limiting (Stricter)
 */
const authRateLimit = rateLimit({
  windowMs: config.rateLimit.authWindowMs,
  max: config.rateLimit.authMaxRequests,
  message: {
    success: false,
    error: {
      message: 'Too many authentication attempts, please try again later.',
      code: 'AUTH_RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: config.rateLimit.skipSuccessfulRequests,
  skipFailedRequests: config.rateLimit.skipFailedRequests,
  onLimitReached: (req) => {
    logger.warn(`Auth rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
  }
});

/**
 * Slow Down Middleware (Progressive delay)
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
  onLimitReached: (req) => {
    logger.warn(`Speed limit reached for IP: ${req.ip}`, {
      ip: req.ip,
      path: req.path
    });
  }
});

/**
 * Input Sanitization Middleware
 */
const sanitizeInput = (req, res, next) => {
  // Sanitize against NoSQL injection
  mongoSanitize.sanitize(req.body);
  mongoSanitize.sanitize(req.query);
  mongoSanitize.sanitize(req.params);

  // XSS Protection
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

/**
 * Sanitize object recursively
 */
const sanitizeObject = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return typeof obj === 'string' ? xss(obj) : obj;
  }

  const sanitized = Array.isArray(obj) ? [] : {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      sanitized[key] = sanitizeObject(obj[key]);
    }
  }
  
  return sanitized;
};

/**
 * Request Size Limiting
 */
const requestSizeLimit = (req, res, next) => {
  const contentLength = parseInt(req.get('content-length'));
  const maxSize = config.upload.maxFileSize;
  
  if (contentLength && contentLength > maxSize) {
    return res.status(413).json({
      success: false,
      error: {
        message: 'Request entity too large',
        code: 'REQUEST_TOO_LARGE'
      }
    });
  }
  
  next();
};

/**
 * Security Audit Logging
 */
const auditLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request
  logger.info('Request received', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - startTime;
    
    // Log response (sanitized)
    logger.info('Response sent', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      success: data?.success !== false
    });

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Account Lockout Middleware
 */
const accountLockout = async (req, res, next) => {
  if (req.path.includes('/login') && req.method === 'POST') {
    const { email } = req.body;
    
    if (!email) {
      return next();
    }

    try {
      const user = await prisma().user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
          id: true,
          email: true,
          loginAttempts: true,
          lockUntil: true,
          status: true
        }
      });

      if (user) {
        // Check if account is locked
        if (user.lockUntil && user.lockUntil > new Date()) {
          const lockTimeRemaining = Math.ceil((user.lockUntil - new Date()) / 1000 / 60);
          return res.status(423).json({
            success: false,
            error: {
              message: `Account is locked. Try again in ${lockTimeRemaining} minutes.`,
              code: 'ACCOUNT_LOCKED'
            }
          });
        }

        // Check if account is disabled
        if (user.status === 'SUSPENDED' || user.status === 'BANNED') {
          return res.status(403).json({
            success: false,
            error: {
              message: 'Account has been suspended. Contact support.',
              code: 'ACCOUNT_SUSPENDED'
            }
          });
        }

        req.user = user;
      }
    } catch (error) {
      logger.error('Account lockout check failed:', error);
    }
  }

  next();
};

/**
 * IP Whitelist/Blacklist Middleware
 */
const ipFilter = (req, res, next) => {
  const clientIP = req.ip;
  
  // Add IP blacklist check here if needed
  const blacklistedIPs = process.env.BLACKLISTED_IPS?.split(',') || [];
  
  if (blacklistedIPs.includes(clientIP)) {
    logger.warn(`Blocked request from blacklisted IP: ${clientIP}`);
    return res.status(403).json({
      success: false,
      error: {
        message: 'Access denied',
        code: 'IP_BLOCKED'
      }
    });
  }

  next();
};

/**
 * Request ID Middleware
 */
const requestId = (req, res, next) => {
  req.id = require('crypto').randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
};

module.exports = {
  securityHeaders,
  corsMiddleware,
  generalRateLimit,
  authRateLimit,
  speedLimiter,
  sanitizeInput,
  requestSizeLimit,
  auditLogger,
  accountLockout,
  ipFilter,
  requestId,
  compression: compression()
};

const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { prisma } = require('../config/database');
const { generateTokenPair, verifyRefreshToken, generateRandomToken } = require('../utils/jwt');
const logger = require('../utils/logger');
const securityConfig = require('../config/security');
const { auditAuth, auditSecurity } = require('../utils/auditLogger');
const PasswordPolicy = require('../utils/passwordPolicy');

const config = securityConfig.getConfig();

/**
 * Register a new user
 */
const register = async (req, res) => {
  const { email, password, firstName, lastName, phone, role = 'CUSTOMER' } = req.body;

  try {
    // Validate password policy
    const passwordValidation = PasswordPolicy.validate(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Password does not meet security requirements',
          details: passwordValidation.errors
        }
      });
    }

    // Check if password is compromised
    const isCompromised = await PasswordPolicy.isCompromised(password);
    if (isCompromised) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'This password has been found in data breaches and cannot be used'
        }
      });
    }

    // Check if user already exists
    const existingUser = await prisma().user.findFirst({
      where: {
        OR: [
          { email: email.toLowerCase() },
          ...(phone ? [{ phone }] : [])
        ]
      }
    });

    if (existingUser) {
      // Log suspicious activity if multiple registration attempts
      await auditSecurity.suspiciousActivity(req, 'duplicate_registration_attempt');

      return res.status(400).json({
        success: false,
        error: {
          message: existingUser.email === email.toLowerCase()
            ? 'Email already registered'
            : 'Phone number already registered'
        }
      });
    }

    // Hash password with secure rounds
    const saltRounds = config.password.bcryptRounds;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Generate email verification token
    const emailVerifyToken = generateRandomToken();

    // Create user
    const user = await prisma().user.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role: role.toUpperCase(),
        emailVerifyToken,
        loginAttempts: 0,
        lockUntil: null,
        createdAt: new Date(),
        lastLoginAt: null,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        createdAt: true,
      }
    });

    // Generate tokens
    const tokens = generateTokenPair(user);

    // Store refresh token in database with additional security info
    await prisma().refreshToken.create({
      data: {
        token: tokens.refreshToken,
        userId: user.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        createdAt: new Date(),
      }
    });

    // Audit log
    await auditAuth.register(user.id, req);

    logger.info(`New user registered: ${user.email}`, {
      userId: user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // TODO: Send verification email
    // await sendVerificationEmail(user.email, emailVerifyToken);

    res.status(201).json({
      success: true,
      data: {
        user,
        tokens,
        message: 'Registration successful. Please check your email for verification.',
        passwordStrength: passwordValidation.strength
      }
    });

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Registration failed'
      }
    });
  }
};

/**
 * Login user
 */
const login = async (req, res) => {
  const { email, password } = req.body;

  try {
    // Find user by email with security fields
    const user = await prisma().user.findUnique({
      where: { email: email.toLowerCase() },
      select: {
        id: true,
        email: true,
        password: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        loginAttempts: true,
        lockUntil: true,
      }
    });

    if (!user) {
      // Log failed login attempt
      await auditAuth.loginFailed(email, req, 'user_not_found');

      return res.status(401).json({
        success: false,
        error: {
          message: 'Invalid email or password'
        }
      });
    }

    // Check if account is locked
    if (user.lockUntil && user.lockUntil > new Date()) {
      const lockTimeRemaining = Math.ceil((user.lockUntil - new Date()) / 1000 / 60);

      await auditAuth.loginLocked(user.id, req);

      return res.status(423).json({
        success: false,
        error: {
          message: `Account is locked due to too many failed login attempts. Try again in ${lockTimeRemaining} minutes.`,
          code: 'ACCOUNT_LOCKED',
          lockTimeRemaining
        }
      });
    }

    // Check if account is active
    if (user.status !== 'ACTIVE') {
      await auditAuth.loginFailed(email, req, 'account_inactive');

      return res.status(401).json({
        success: false,
        error: {
          message: user.status === 'SUSPENDED' ? 'Account has been suspended' : 'Account is not active'
        }
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      // Increment login attempts
      const newAttempts = (user.loginAttempts || 0) + 1;
      const maxAttempts = config.password.maxAttempts;

      let updateData = { loginAttempts: newAttempts };

      // Lock account if max attempts reached
      if (newAttempts >= maxAttempts) {
        updateData.lockUntil = new Date(Date.now() + config.password.lockoutDuration);
        updateData.loginAttempts = 0; // Reset attempts after lock
      }

      await prisma().user.update({
        where: { id: user.id },
        data: updateData
      });

      await auditAuth.loginFailed(email, req, 'invalid_password');

      const remainingAttempts = Math.max(0, maxAttempts - newAttempts);

      return res.status(401).json({
        success: false,
        error: {
          message: 'Invalid email or password',
          remainingAttempts: newAttempts >= maxAttempts ? 0 : remainingAttempts
        }
      });
    }

    // Successful login - reset login attempts and update last login
    await prisma().user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        loginAttempts: 0,
        lockUntil: null
      }
    });

    // Generate tokens with enhanced security
    const userForToken = {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
    };
    const tokens = generateTokenPair(userForToken);

    // Store refresh token in database with security metadata
    await prisma().refreshToken.create({
      data: {
        token: tokens.refreshToken,
        userId: user.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        createdAt: new Date(),
      }
    });

    // Audit successful login
    await auditAuth.loginSuccess(user.id, req);

    // Remove password and sensitive fields from response
    const { password: _, loginAttempts: __, lockUntil: ___, ...userResponse } = user;

    logger.info(`User logged in: ${user.email}`, {
      userId: user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        user: userResponse,
        tokens,
        message: 'Login successful'
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Login failed'
      }
    });
  }
};

/**
 * Refresh access token
 */
const refreshToken = async (req, res) => {
  const { refreshToken: token } = req.body;

  if (!token) {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Refresh token is required'
      }
    });
  }

  try {
    // Verify refresh token
    const decoded = verifyRefreshToken(token);

    // Check if refresh token exists in database
    const storedToken = await prisma().refreshToken.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            emailVerified: true,
          }
        }
      }
    });

    if (!storedToken || storedToken.expiresAt < new Date()) {
      return res.status(401).json({
        success: false,
        error: {
          message: 'Invalid or expired refresh token'
        }
      });
    }

    // Check if user is still active
    if (storedToken.user.status !== 'ACTIVE') {
      return res.status(401).json({
        success: false,
        error: {
          message: 'Account is not active'
        }
      });
    }

    // Generate new tokens
    const tokens = generateTokenPair(storedToken.user);

    // Delete old refresh token and create new one
    await prisma().$transaction([
      prisma().refreshToken.delete({
        where: { token }
      }),
      prisma().refreshToken.create({
        data: {
          token: tokens.refreshToken,
          userId: storedToken.user.id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        tokens
      }
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: 'Invalid refresh token'
      }
    });
  }
};

/**
 * Logout user
 */
const logout = async (req, res) => {
  const { refreshToken: token } = req.body;

  try {
    if (token) {
      // Delete refresh token from database
      await prisma().refreshToken.deleteMany({
        where: { token }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Logged out successfully'
      }
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Logout failed'
      }
    });
  }
};

/**
 * Get current user profile
 */
const getProfile = async (req, res) => {
  try {
    const user = await prisma().user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        avatar: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    res.json({
      success: true,
      data: { user }
    });

  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get profile'
      }
    });
  }
};

/**
 * Verify email with token
 */
const verifyEmail = async (req, res) => {
  const { token } = req.body;

  try {
    // Find user with verification token
    const user = await prisma().user.findFirst({
      where: { emailVerifyToken: token }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid or expired verification token'
        }
      });
    }

    // Update user as verified
    await prisma().user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerifyToken: null,
      }
    });

    logger.info(`Email verified for user: ${user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Email verified successfully'
      }
    });

  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Email verification failed'
      }
    });
  }
};

/**
 * Request password reset
 */
const requestPasswordReset = async (req, res) => {
  const { email } = req.body;

  try {
    // Find user by email
    const user = await prisma().user.findUnique({
      where: { email }
    });

    if (!user) {
      // Don't reveal if email exists for security
      return res.json({
        success: true,
        data: {
          message: 'If the email exists, a password reset link has been sent'
        }
      });
    }

    // Generate reset token
    const resetToken = generateRandomToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Update user with reset token
    await prisma().user.update({
      where: { id: user.id },
      data: {
        resetPasswordToken: resetToken,
        resetPasswordExpires: resetExpires,
      }
    });

    logger.info(`Password reset requested for user: ${user.email}`);

    // TODO: Send password reset email
    // await sendPasswordResetEmail(user.email, resetToken);

    res.json({
      success: true,
      data: {
        message: 'If the email exists, a password reset link has been sent'
      }
    });

  } catch (error) {
    logger.error('Password reset request error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Password reset request failed'
      }
    });
  }
};

/**
 * Confirm password reset
 */
const confirmPasswordReset = async (req, res) => {
  const { token, password } = req.body;

  try {
    // Find user with valid reset token
    const user = await prisma().user.findFirst({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: {
          gt: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid or expired reset token'
        }
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user password and clear reset token
    await prisma().user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      }
    });

    // Invalidate all refresh tokens for security
    await prisma().refreshToken.deleteMany({
      where: { userId: user.id }
    });

    logger.info(`Password reset completed for user: ${user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Password reset successfully'
      }
    });

  } catch (error) {
    logger.error('Password reset confirmation error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Password reset failed'
      }
    });
  }
};

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  verifyEmail,
  requestPasswordReset,
  confirmPasswordReset,
};

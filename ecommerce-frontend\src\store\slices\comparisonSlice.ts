import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Product } from '../../types';
import toast from 'react-hot-toast';

interface ComparisonState {
  products: Product[];
  maxProducts: number;
}

const initialState: ComparisonState = {
  products: [],
  maxProducts: 4, // Maximum number of products that can be compared
};

const comparisonSlice = createSlice({
  name: 'comparison',
  initialState,
  reducers: {
    addToComparison: (state, action: PayloadAction<Product>) => {
      const product = action.payload;
      
      // Check if product is already in comparison
      const existingIndex = state.products.findIndex(p => p.id === product.id);
      if (existingIndex !== -1) {
        toast.error('Product is already in comparison');
        return;
      }

      // Check if we've reached the maximum number of products
      if (state.products.length >= state.maxProducts) {
        toast.error(`You can only compare up to ${state.maxProducts} products`);
        return;
      }

      state.products.push(product);
      toast.success('Product added to comparison');
    },

    removeFromComparison: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      state.products = state.products.filter(p => p.id !== productId);
      toast.success('Product removed from comparison');
    },

    clearComparison: (state) => {
      state.products = [];
      toast.success('Comparison cleared');
    },

    replaceInComparison: (state, action: PayloadAction<{ index: number; product: Product }>) => {
      const { index, product } = action.payload;
      
      // Check if product is already in comparison at a different index
      const existingIndex = state.products.findIndex(p => p.id === product.id);
      if (existingIndex !== -1 && existingIndex !== index) {
        toast.error('Product is already in comparison');
        return;
      }

      if (index >= 0 && index < state.products.length) {
        state.products[index] = product;
        toast.success('Product replaced in comparison');
      }
    },
  },
});

export const {
  addToComparison,
  removeFromComparison,
  clearComparison,
  replaceInComparison,
} = comparisonSlice.actions;

export default comparisonSlice.reducer;

import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../store';
import { apiHelpers } from '../../config/api';
import {
  ClockIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { Order } from '../../types';

const OrdersPage: React.FC = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      fetchOrders();
    }
  }, [isAuthenticated]);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      // This will be implemented when backend order endpoints are ready
      // const response: any = await apiHelpers.get('/orders');
      // if (response.success) {
      //   setOrders(response.data.orders);
      // }

      // Mock data for demonstration
      setTimeout(() => {
        setOrders([
          {
            id: '1',
            orderNumber: 'ORD-2024-001',
            userId: 'user1',
            status: 'DELIVERED',
            paymentStatus: 'COMPLETED',
            subtotal: 89.99,
            taxAmount: 7.20,
            shippingAmount: 0,
            discountAmount: 0,
            totalAmount: 97.19,
            currency: 'USD',
            shippingAddressId: 'addr1',
            items: [
              {
                id: 'item1',
                orderId: '1',
                productId: 'prod1',
                quantity: 2,
                unitPrice: 44.99,
                totalPrice: 89.98,
                product: {
                  id: 'prod1',
                  name: 'Wireless Bluetooth Headphones',
                  slug: 'wireless-bluetooth-headphones',
                  description: 'High-quality wireless headphones',
                  sku: 'WBH-001',
                  price: 44.99,
                  trackQuantity: true,
                  quantity: 50,
                  allowBackorder: false,
                  images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300'],
                  status: 'APPROVED',
                  featured: false,
                  tags: ['electronics', 'audio'],
                  sellerId: 'seller1',
                  categoryId: 'cat1',
                  createdAt: '2024-01-15T10:00:00Z',
                  updatedAt: '2024-01-15T10:00:00Z',
                },
                createdAt: '2024-01-15T10:00:00Z',
              },
            ],
            shippingAddress: {
              id: 'addr1',
              userId: 'user1',
              firstName: 'John',
              lastName: 'Doe',
              addressLine1: '123 Main St',
              city: 'New York',
              state: 'NY',
              postalCode: '10001',
              country: 'United States',
              isDefault: true,
              type: 'shipping',
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            },
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-20T15:30:00Z',
            deliveredAt: '2024-01-20T15:30:00Z',
          },
          {
            id: '2',
            orderNumber: 'ORD-2024-002',
            userId: 'user1',
            status: 'SHIPPED',
            paymentStatus: 'COMPLETED',
            subtotal: 129.99,
            taxAmount: 10.40,
            shippingAmount: 9.99,
            discountAmount: 10.00,
            totalAmount: 140.38,
            currency: 'USD',
            shippingAddressId: 'addr1',
            trackingNumber: 'TRK123456789',
            items: [
              {
                id: 'item2',
                orderId: '2',
                productId: 'prod2',
                quantity: 1,
                unitPrice: 129.99,
                totalPrice: 129.99,
                product: {
                  id: 'prod2',
                  name: 'Smart Fitness Watch',
                  slug: 'smart-fitness-watch',
                  description: 'Advanced fitness tracking watch',
                  sku: 'SFW-001',
                  price: 129.99,
                  trackQuantity: true,
                  quantity: 25,
                  allowBackorder: false,
                  images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300'],
                  status: 'APPROVED',
                  featured: true,
                  tags: ['electronics', 'fitness'],
                  sellerId: 'seller2',
                  categoryId: 'cat1',
                  createdAt: '2024-01-10T10:00:00Z',
                  updatedAt: '2024-01-10T10:00:00Z',
                },
                createdAt: '2024-01-18T14:00:00Z',
              },
            ],
            shippingAddress: {
              id: 'addr1',
              userId: 'user1',
              firstName: 'John',
              lastName: 'Doe',
              addressLine1: '123 Main St',
              city: 'New York',
              state: 'NY',
              postalCode: '10001',
              country: 'United States',
              isDefault: true,
              type: 'shipping',
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            },
            createdAt: '2024-01-18T14:00:00Z',
            updatedAt: '2024-01-19T09:15:00Z',
            shippedAt: '2024-01-19T09:15:00Z',
          },
        ]);
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      setError('Failed to fetch orders');
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'PROCESSING':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      case 'SHIPPED':
        return <TruckIcon className="h-5 w-5 text-blue-600" />;
      case 'DELIVERED':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'CANCELLED':
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800';
      case 'SHIPPED':
        return 'bg-blue-100 text-blue-800';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <h2 className="text-lg font-medium text-gray-900">Please sign in to view your orders</h2>
          <p className="mt-2 text-gray-500">You need to be logged in to access your order history.</p>
          <Link
            to="/login"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Order History</h1>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="h-6 bg-gray-200 rounded w-32"></div>
                <div className="h-6 bg-gray-200 rounded w-24"></div>
              </div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Order History</h1>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Order History</h1>

      {orders.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h2 className="mt-4 text-lg font-medium text-gray-900">No orders yet</h2>
          <p className="mt-2 text-gray-500">Start shopping to see your orders here.</p>
          <Link
            to="/products"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Start Shopping
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {orders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* Order Header */}
              <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        Order #{order.orderNumber}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Placed on {formatDate(order.createdAt)}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(order.status)}
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-gray-900">
                      {formatPrice(order.totalAmount)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="px-6 py-4">
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4">
                      <img
                        src={item.product.images[0] || 'https://via.placeholder.com/80x80'}
                        alt={item.product.name}
                        className="w-16 h-16 object-cover rounded-md"
                      />
                      <div className="flex-1">
                        <Link
                          to={`/products/${item.product.id}`}
                          className="text-sm font-medium text-gray-900 hover:text-primary-600"
                        >
                          {item.product.name}
                        </Link>
                        <p className="text-sm text-gray-500">
                          Quantity: {item.quantity} × {formatPrice(item.unitPrice)}
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatPrice(item.totalPrice)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Actions */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {order.trackingNumber && (
                      <p className="text-sm text-gray-600">
                        Tracking: <span className="font-medium">{order.trackingNumber}</span>
                      </p>
                    )}
                    {order.deliveredAt && (
                      <p className="text-sm text-green-600">
                        Delivered on {formatDate(order.deliveredAt)}
                      </p>
                    )}
                    {order.shippedAt && !order.deliveredAt && (
                      <p className="text-sm text-blue-600">
                        Shipped on {formatDate(order.shippedAt)}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                      <EyeIcon className="h-4 w-4 mr-2" />
                      View Details
                    </button>
                    {order.status === 'DELIVERED' && (
                      <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Buy Again
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Demo Notice */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
        <p className="text-blue-800 text-sm">
          <strong>Demo Notice:</strong> This page shows mock order data for demonstration purposes.
          Real order data will be displayed when the backend order management system is implemented.
        </p>
      </div>
    </div>
  );
};

export default OrdersPage;

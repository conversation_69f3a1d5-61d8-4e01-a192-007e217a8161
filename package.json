{"name": "ecommerce-backend-api", "version": "1.0.0", "description": "Production-ready e-commerce backend API with Express.js and PostgreSQL", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "node src/database/seeders/index.js", "db:reset": "prisma migrate reset", "db:studio": "prisma studio", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o swagger.json"}, "keywords": ["ecommerce", "api", "express", "postgresql", "prisma", "jwt", "rest"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.0.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "stripe": "^14.9.0", "validator": "^13.15.15", "winston": "^3.11.0", "xss": "^1.0.15"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
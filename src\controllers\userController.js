const bcrypt = require('bcryptjs');
const { prisma } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Update user profile
 */
const updateProfile = async (req, res) => {
  const { firstName, lastName, phone } = req.body;
  const userId = req.user.id;

  try {
    // Check if phone number is already taken by another user
    if (phone) {
      const existingUser = await prisma().user.findFirst({
        where: {
          phone,
          id: { not: userId }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Phone number already in use'
          }
        });
      }
    }

    // Update user profile
    const updatedUser = await prisma().user.update({
      where: { id: userId },
      data: {
        ...(firstName && { firstName }),
        ...(lastName && { lastName }),
        ...(phone && { phone }),
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        avatar: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    logger.info(`Profile updated for user: ${updatedUser.email}`);

    res.json({
      success: true,
      data: {
        user: updatedUser
      }
    });

  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update profile'
      }
    });
  }
};

/**
 * Change user password
 */
const changePassword = async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  try {
    // Get user with password
    const user = await prisma().user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        password: true,
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'User not found'
        }
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Current password is incorrect'
        }
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await prisma().user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    });

    // Invalidate all refresh tokens for security
    await prisma().refreshToken.deleteMany({
      where: { userId }
    });

    logger.info(`Password changed for user: ${user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Password changed successfully'
      }
    });

  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to change password'
      }
    });
  }
};

/**
 * Get user addresses
 */
const getAddresses = async (req, res) => {
  const userId = req.user.id;

  try {
    const addresses = await prisma().address.findMany({
      where: { userId },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    res.json({
      success: true,
      data: {
        addresses
      }
    });

  } catch (error) {
    logger.error('Get addresses error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get addresses'
      }
    });
  }
};

/**
 * Add new address
 */
const addAddress = async (req, res) => {
  const {
    firstName,
    lastName,
    company,
    addressLine1,
    addressLine2,
    city,
    state,
    postalCode,
    country,
    phone,
    isDefault,
    type
  } = req.body;
  const userId = req.user.id;

  try {
    // If this is set as default, unset other default addresses of the same type
    if (isDefault) {
      await prisma().address.updateMany({
        where: {
          userId,
          type: type || 'shipping'
        },
        data: { isDefault: false }
      });
    }

    const address = await prisma().address.create({
      data: {
        userId,
        firstName,
        lastName,
        company,
        addressLine1,
        addressLine2,
        city,
        state,
        postalCode,
        country,
        phone,
        isDefault: isDefault || false,
        type: type || 'shipping'
      }
    });

    logger.info(`Address added for user: ${req.user.email}`);

    res.status(201).json({
      success: true,
      data: {
        address
      }
    });

  } catch (error) {
    logger.error('Add address error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to add address'
      }
    });
  }
};

/**
 * Update address
 */
const updateAddress = async (req, res) => {
  const { addressId } = req.params;
  const userId = req.user.id;
  const updateData = req.body;

  try {
    // Check if address belongs to user
    const existingAddress = await prisma().address.findFirst({
      where: {
        id: addressId,
        userId
      }
    });

    if (!existingAddress) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Address not found'
        }
      });
    }

    // If setting as default, unset other default addresses of the same type
    if (updateData.isDefault) {
      await prisma().address.updateMany({
        where: {
          userId,
          type: updateData.type || existingAddress.type,
          id: { not: addressId }
        },
        data: { isDefault: false }
      });
    }

    const updatedAddress = await prisma().address.update({
      where: { id: addressId },
      data: updateData
    });

    logger.info(`Address updated for user: ${req.user.email}`);

    res.json({
      success: true,
      data: {
        address: updatedAddress
      }
    });

  } catch (error) {
    logger.error('Update address error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update address'
      }
    });
  }
};

/**
 * Delete address
 */
const deleteAddress = async (req, res) => {
  const { addressId } = req.params;
  const userId = req.user.id;

  try {
    // Check if address belongs to user
    const existingAddress = await prisma().address.findFirst({
      where: {
        id: addressId,
        userId
      }
    });

    if (!existingAddress) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Address not found'
        }
      });
    }

    await prisma().address.delete({
      where: { id: addressId }
    });

    logger.info(`Address deleted for user: ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Address deleted successfully'
      }
    });

  } catch (error) {
    logger.error('Delete address error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete address'
      }
    });
  }
};

/**
 * Get user profile
 */
const getProfile = async (req, res) => {
  const userId = req.user.id;

  try {
    const user = await prisma().user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        avatar: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            orders: true,
            reviews: true,
            cart: true,
            wishlist: true,
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'User not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        user
      }
    });

  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get profile'
      }
    });
  }
};

/**
 * Get user order history
 */
const getOrderHistory = async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 10, status } = req.query;

  try {
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const whereClause = {
      userId,
      ...(status && { status: status.toUpperCase() })
    };

    const [orders, totalCount] = await Promise.all([
      prisma().order.findMany({
        where: whereClause,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  images: true,
                }
              }
            }
          },
          shippingAddress: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: parseInt(limit),
      }),
      prisma().order.count({ where: whereClause })
    ]);

    const totalPages = Math.ceil(totalCount / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        }
      }
    });

  } catch (error) {
    logger.error('Get order history error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get order history'
      }
    });
  }
};

/**
 * Get user wishlist
 */
const getWishlist = async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20 } = req.query;

  try {
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [wishlistItems, totalCount] = await Promise.all([
      prisma().wishlistItem.findMany({
        where: { userId },
        include: {
          product: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                }
              },
              seller: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                }
              },
              reviews: {
                select: {
                  rating: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: parseInt(limit),
      }),
      prisma().wishlistItem.count({ where: { userId } })
    ]);

    // Calculate average ratings for products
    const wishlistWithRatings = wishlistItems.map(item => ({
      ...item,
      product: {
        ...item.product,
        averageRating: item.product.reviews.length > 0
          ? item.product.reviews.reduce((sum, review) => sum + review.rating, 0) / item.product.reviews.length
          : 0,
        reviewCount: item.product.reviews.length,
        reviews: undefined, // Remove reviews array from response
      }
    }));

    const totalPages = Math.ceil(totalCount / parseInt(limit));

    res.json({
      success: true,
      data: {
        wishlist: wishlistWithRatings,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        }
      }
    });

  } catch (error) {
    logger.error('Get wishlist error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get wishlist'
      }
    });
  }
};

module.exports = {
  updateProfile,
  changePassword,
  getAddresses,
  addAddress,
  updateAddress,
  deleteAddress,
  getProfile,
  getOrderHistory,
  getWishlist,
};

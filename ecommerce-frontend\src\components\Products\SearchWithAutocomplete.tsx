import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { searchProducts } from '../../store/slices/productSlice';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useDebounce } from '../../hooks/useDebounce';

interface SearchWithAutocompleteProps {
  placeholder?: string;
  className?: string;
}

const SearchWithAutocomplete: React.FC<SearchWithAutocompleteProps> = ({
  placeholder = "Search products...",
  className = ""
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { searchResults, isLoading } = useAppSelector((state) => state.products);
  
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const debouncedQuery = useDebounce(query, 300);

  // Popular search terms (this would typically come from the backend)
  const popularSearches = [
    'iPhone', 'Samsung Galaxy', 'MacBook', 'Nike shoes', 'Headphones',
    'Laptop', 'Smartphone', 'Dress', 'Jeans', 'Sneakers'
  ];

  useEffect(() => {
    if (debouncedQuery.length >= 2) {
      dispatch(searchProducts({ q: debouncedQuery, filters: { limit: 8 } }));
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [debouncedQuery, dispatch]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const items = query.length >= 2 ? searchResults : popularSearches;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < items.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (query.length >= 2) {
            const selectedProduct = searchResults[selectedIndex];
            navigate(`/products/${selectedProduct.id}`);
          } else {
            const selectedSearch = popularSearches[selectedIndex];
            handleSearch(selectedSearch);
          }
        } else if (query.trim()) {
          handleSearch(query);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      navigate(`/products?q=${encodeURIComponent(searchQuery.trim())}`);
      setQuery(searchQuery);
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  const handleProductClick = (productId: string) => {
    navigate(`/products/${productId}`);
    setIsOpen(false);
    setQuery('');
  };

  const handlePopularSearchClick = (searchTerm: string) => {
    handleSearch(searchTerm);
  };

  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
        />
        {query && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
              onClick={clearSearch}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-96 overflow-y-auto">
          {query.length >= 2 ? (
            // Search Results
            <>
              {isLoading ? (
                <div className="px-4 py-3 text-sm text-gray-500">Searching...</div>
              ) : searchResults.length > 0 ? (
                <>
                  <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b">
                    Products
                  </div>
                  {searchResults.map((product, index) => (
                    <button
                      key={product.id}
                      onClick={() => handleProductClick(product.id)}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                        selectedIndex === index ? 'bg-gray-50' : ''
                      }`}
                    >
                      <img
                        src={product.images[0] || 'https://via.placeholder.com/40x40'}
                        alt={product.name}
                        className="w-10 h-10 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {product.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatPrice(product.price)}
                        </div>
                      </div>
                    </button>
                  ))}
                  {searchResults.length >= 8 && (
                    <button
                      onClick={() => handleSearch(query)}
                      className="w-full px-4 py-3 text-left text-sm text-primary-600 hover:bg-gray-50 border-t"
                    >
                      View all results for "{query}"
                    </button>
                  )}
                </>
              ) : (
                <div className="px-4 py-3 text-sm text-gray-500">
                  No products found for "{query}"
                </div>
              )}
            </>
          ) : (
            // Popular Searches
            <>
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b">
                Popular Searches
              </div>
              {popularSearches.map((searchTerm, index) => (
                <button
                  key={searchTerm}
                  onClick={() => handlePopularSearchClick(searchTerm)}
                  className={`w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 ${
                    selectedIndex === index ? 'bg-gray-50' : ''
                  }`}
                >
                  <MagnifyingGlassIcon className="inline h-4 w-4 mr-2 text-gray-400" />
                  {searchTerm}
                </button>
              ))}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchWithAutocomplete;

const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

let prisma;

const connectDB = async () => {
  try {
    if (!prisma) {
      prisma = new PrismaClient({
        log: process.env.NODE_ENV === 'development' 
          ? ['query', 'info', 'warn', 'error']
          : ['error'],
        errorFormat: 'pretty',
      });

      // Connect to the database
      await prisma.$connect();
      
      logger.info('✅ Database connected successfully');
      
      // Test the connection
      await prisma.$queryRaw`SELECT 1`;
      logger.info('✅ Database connection tested successfully');
    }
    
    return prisma;
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    throw error;
  }
};

const disconnectDB = async () => {
  try {
    if (prisma) {
      await prisma.$disconnect();
      logger.info('✅ Database disconnected successfully');
    }
  } catch (error) {
    logger.error('❌ Database disconnection failed:', error);
    throw error;
  }
};

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectDB();
});

process.on('SIGINT', async () => {
  await disconnectDB();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDB();
  process.exit(0);
});

module.exports = {
  connectDB,
  disconnectDB,
  prisma: () => prisma
};

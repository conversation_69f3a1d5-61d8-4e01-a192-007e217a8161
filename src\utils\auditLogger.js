const { prisma } = require('../config/database');
const logger = require('./logger');
const securityConfig = require('../config/security');

const config = securityConfig.getConfig();

/**
 * Audit Log Entry Types
 */
const AUDIT_EVENTS = {
  // Authentication Events
  USER_LOGIN_SUCCESS: 'user.login.success',
  USER_LOGIN_FAILED: 'user.login.failed',
  USER_LOGIN_LOCKED: 'user.login.locked',
  USER_LOGOUT: 'user.logout',
  USER_REGISTER: 'user.register',
  USER_EMAIL_VERIFY: 'user.email.verify',
  USER_PASSWORD_RESET_REQUEST: 'user.password.reset.request',
  USER_PASSWORD_RESET_CONFIRM: 'user.password.reset.confirm',
  USER_PASSWORD_CHANGE: 'user.password.change',
  
  // Profile Events
  USER_PROFILE_UPDATE: 'user.profile.update',
  USER_PROFILE_VIEW: 'user.profile.view',
  
  // Order Events
  ORDER_CREATE: 'order.create',
  ORDER_UPDATE: 'order.update',
  ORDER_CANCEL: 'order.cancel',
  ORDER_REFUND: 'order.refund',
  
  // Payment Events
  PAYMENT_PROCESS: 'payment.process',
  PAYMENT_SUCCESS: 'payment.success',
  PAYMENT_FAILED: 'payment.failed',
  PAYMENT_REFUND: 'payment.refund',
  
  // Admin Events
  ADMIN_USER_CREATE: 'admin.user.create',
  ADMIN_USER_UPDATE: 'admin.user.update',
  ADMIN_USER_DELETE: 'admin.user.delete',
  ADMIN_USER_SUSPEND: 'admin.user.suspend',
  ADMIN_PRODUCT_CREATE: 'admin.product.create',
  ADMIN_PRODUCT_UPDATE: 'admin.product.update',
  ADMIN_PRODUCT_DELETE: 'admin.product.delete',
  
  // Security Events
  SECURITY_RATE_LIMIT_EXCEEDED: 'security.rate_limit.exceeded',
  SECURITY_SUSPICIOUS_ACTIVITY: 'security.suspicious.activity',
  SECURITY_IP_BLOCKED: 'security.ip.blocked',
  SECURITY_UNAUTHORIZED_ACCESS: 'security.unauthorized.access',
};

/**
 * Log audit event
 */
const auditLog = async (event, details = {}) => {
  try {
    if (!config.audit.enabled) {
      return;
    }

    // Check if this event type should be audited
    if (!config.audit.sensitiveOperations.includes(event)) {
      return;
    }

    const auditEntry = {
      event,
      timestamp: new Date(),
      userId: details.userId || null,
      sessionId: details.sessionId || null,
      ipAddress: details.ipAddress || null,
      userAgent: details.userAgent || null,
      resource: details.resource || null,
      action: details.action || null,
      result: details.result || 'success',
      metadata: details.metadata ? JSON.stringify(securityConfig.sanitizeForLogging(details.metadata)) : null,
      severity: details.severity || 'info'
    };

    // Store in database
    await prisma().auditLog.create({
      data: auditEntry
    });

    // Also log to application logger for immediate visibility
    logger.info(`AUDIT: ${event}`, {
      userId: auditEntry.userId,
      ipAddress: auditEntry.ipAddress,
      result: auditEntry.result,
      resource: auditEntry.resource
    });

  } catch (error) {
    // Don't let audit logging failures break the application
    logger.error('Audit logging failed:', error);
  }
};

/**
 * Log authentication events
 */
const auditAuth = {
  loginSuccess: (userId, req) => auditLog(AUDIT_EVENTS.USER_LOGIN_SUCCESS, {
    userId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'success'
  }),

  loginFailed: (email, req, reason = 'invalid_credentials') => auditLog(AUDIT_EVENTS.USER_LOGIN_FAILED, {
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'failed',
    metadata: { email, reason }
  }),

  loginLocked: (userId, req) => auditLog(AUDIT_EVENTS.USER_LOGIN_LOCKED, {
    userId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'locked',
    severity: 'warning'
  }),

  logout: (userId, req) => auditLog(AUDIT_EVENTS.USER_LOGOUT, {
    userId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'success'
  }),

  register: (userId, req) => auditLog(AUDIT_EVENTS.USER_REGISTER, {
    userId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'success'
  }),

  passwordReset: (userId, req) => auditLog(AUDIT_EVENTS.USER_PASSWORD_RESET_CONFIRM, {
    userId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
    result: 'success',
    severity: 'warning'
  })
};

/**
 * Log security events
 */
const auditSecurity = {
  rateLimitExceeded: (req, limitType) => auditLog(AUDIT_EVENTS.SECURITY_RATE_LIMIT_EXCEEDED, {
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    resource: req.path,
    result: 'blocked',
    metadata: { limitType },
    severity: 'warning'
  }),

  suspiciousActivity: (req, activity) => auditLog(AUDIT_EVENTS.SECURITY_SUSPICIOUS_ACTIVITY, {
    userId: req.user?.id,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    resource: req.path,
    result: 'detected',
    metadata: { activity },
    severity: 'high'
  }),

  unauthorizedAccess: (req, resource) => auditLog(AUDIT_EVENTS.SECURITY_UNAUTHORIZED_ACCESS, {
    userId: req.user?.id,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    resource,
    result: 'denied',
    severity: 'high'
  })
};

/**
 * Log admin events
 */
const auditAdmin = {
  userAction: (adminUserId, targetUserId, action, req) => auditLog(`admin.user.${action}`, {
    userId: adminUserId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    resource: `user:${targetUserId}`,
    action,
    result: 'success',
    severity: 'high'
  }),

  productAction: (adminUserId, productId, action, req) => auditLog(`admin.product.${action}`, {
    userId: adminUserId,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    resource: `product:${productId}`,
    action,
    result: 'success'
  })
};

/**
 * Get audit logs with filtering
 */
const getAuditLogs = async (filters = {}) => {
  try {
    const {
      userId,
      event,
      startDate,
      endDate,
      ipAddress,
      severity,
      page = 1,
      limit = 50
    } = filters;

    const where = {};
    
    if (userId) where.userId = userId;
    if (event) where.event = event;
    if (ipAddress) where.ipAddress = ipAddress;
    if (severity) where.severity = severity;
    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = new Date(startDate);
      if (endDate) where.timestamp.lte = new Date(endDate);
    }

    const [logs, total] = await Promise.all([
      prisma().auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true
            }
          }
        }
      }),
      prisma().auditLog.count({ where })
    ]);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

  } catch (error) {
    logger.error('Failed to retrieve audit logs:', error);
    throw error;
  }
};

module.exports = {
  auditLog,
  auditAuth,
  auditSecurity,
  auditAdmin,
  getAuditLogs,
  AUDIT_EVENTS
};

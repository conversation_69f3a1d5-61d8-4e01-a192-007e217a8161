import React from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { removeFromComparison, clearComparison } from '../../store/slices/comparisonSlice';
import { addToCart, fetchCart } from '../../store/slices/cartSlice';
import { StarIcon, ShoppingCartIcon, XMarkIcon, TrashIcon } from '@heroicons/react/24/outline';


const ComparisonPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { products } = useAppSelector((state) => state.comparison);
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  const handleAddToCart = async (productId: string) => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    try {
      await dispatch(addToCart({ productId, quantity: 1 })).unwrap();
      dispatch(fetchCart());
    } catch (error) {
      // Error is handled by the slice and toast
    }
  };

  const handleRemoveFromComparison = (productId: string) => {
    dispatch(removeFromComparison(productId));
  };

  const handleClearComparison = () => {
    if (window.confirm('Are you sure you want to clear all products from comparison?')) {
      dispatch(clearComparison());
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center justify-center">
        {[...Array(5)].map((_, i) => (
          <StarIcon
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  if (products.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products to compare</h3>
          <p className="text-gray-600 mb-8">Add products to comparison to see them side by side!</p>
          <Link
            to="/products"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Browse Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Product Comparison</h1>
        <button
          onClick={handleClearComparison}
          className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
        >
          <TrashIcon className="h-4 w-4 mr-2" />
          Clear All
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200 rounded-lg">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider w-48">
                Feature
              </th>
              {products.map((product) => (
                <th key={product.id} className="px-6 py-4 text-center border-l border-gray-200 min-w-64">
                  <div className="relative">
                    <button
                      onClick={() => handleRemoveFromComparison(product.id)}
                      className="absolute top-0 right-0 p-1 text-gray-400 hover:text-red-500"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                    <Link to={`/products/${product.id}`}>
                      <img
                        src={product.images[0] || 'https://via.placeholder.com/200x150'}
                        alt={product.name}
                        className="w-32 h-24 object-cover mx-auto mb-2 rounded"
                      />
                      <h3 className="text-sm font-medium text-gray-900 hover:text-primary-600 line-clamp-2">
                        {product.name}
                      </h3>
                    </Link>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {/* Price */}
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Price</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  <div className="space-y-1">
                    <div className="text-lg font-bold text-gray-900">
                      {formatPrice(product.price)}
                    </div>
                    {product.comparePrice && product.comparePrice > product.price && (
                      <div className="text-sm text-gray-500 line-through">
                        {formatPrice(product.comparePrice)}
                      </div>
                    )}
                  </div>
                </td>
              ))}
            </tr>

            {/* Rating */}
            <tr className="bg-gray-50">
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Rating</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  {product.averageRating ? (
                    renderStars(product.averageRating)
                  ) : (
                    <span className="text-gray-500">No ratings</span>
                  )}
                </td>
              ))}
            </tr>

            {/* Stock Status */}
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Availability</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  {product.quantity > 0 ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      In Stock
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Out of Stock
                    </span>
                  )}
                </td>
              ))}
            </tr>

            {/* Category */}
            <tr className="bg-gray-50">
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Category</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  <span className="text-sm text-gray-600">
                    {product.category?.name || 'N/A'}
                  </span>
                </td>
              ))}
            </tr>

            {/* Description */}
            <tr>
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Description</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {product.shortDescription || product.description}
                  </p>
                </td>
              ))}
            </tr>

            {/* Actions */}
            <tr className="bg-gray-50">
              <td className="px-6 py-4 text-sm font-medium text-gray-900">Actions</td>
              {products.map((product) => (
                <td key={product.id} className="px-6 py-4 text-center border-l border-gray-200">
                  <div className="space-y-2">
                    <button
                      onClick={() => handleAddToCart(product.id)}
                      disabled={product.quantity === 0}
                      className="w-full flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ShoppingCartIcon className="h-4 w-4 mr-2" />
                      {product.quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
                    </button>
                    <Link
                      to={`/products/${product.id}`}
                      className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      View Details
                    </Link>
                  </div>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>

      {products.length < 4 && (
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-4">
            You can compare up to 4 products. Add more products to get a better comparison.
          </p>
          <Link
            to="/products"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Browse More Products
          </Link>
        </div>
      )}
    </div>
  );
};

export default ComparisonPage;

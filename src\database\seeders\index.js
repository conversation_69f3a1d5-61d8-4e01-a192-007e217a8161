const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const logger = require('../../utils/logger');

const prisma = new PrismaClient();

async function seedUsers() {
  logger.info('Seeding users...');

  // Create admin user
  const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'Admin123!@#', 12);

  const admin = await prisma.user.upsert({
    where: { email: process.env.ADMIN_EMAIL || '<EMAIL>' },
    update: {},
    create: {
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      status: 'ACTIVE',
      emailVerified: true,
    },
  });

  logger.info(`Admin user created: ${admin.email}`);

  // Create multiple sellers
  const sellers = [];
  const sellerData = [
    { email: '<EMAIL>', firstName: 'John', lastName: 'Seller' },
    { email: '<EMAIL>', firstName: 'Sarah', lastName: 'Electronics' },
    { email: '<EMAIL>', firstName: 'Mike', lastName: 'Fashion' },
    { email: '<EMAIL>', firstName: 'Lisa', lastName: 'Home' },
  ];

  for (const sellerInfo of sellerData) {
    const sellerPassword = await bcrypt.hash('Seller123!@#', 12);

    const seller = await prisma.user.upsert({
      where: { email: sellerInfo.email },
      update: {},
      create: {
        email: sellerInfo.email,
        password: sellerPassword,
        firstName: sellerInfo.firstName,
        lastName: sellerInfo.lastName,
        role: 'SELLER',
        status: 'ACTIVE',
        emailVerified: true,
      },
    });

    sellers.push(seller);
    logger.info(`Seller user created: ${seller.email}`);
  }

  // Create multiple customers
  const customers = [];
  const customerData = [
    { email: '<EMAIL>', firstName: 'Jane', lastName: 'Customer' },
    { email: '<EMAIL>', firstName: 'Bob', lastName: 'Smith' },
    { email: '<EMAIL>', firstName: 'Alice', lastName: 'Johnson' },
    { email: '<EMAIL>', firstName: 'David', lastName: 'Brown' },
    { email: '<EMAIL>', firstName: 'Emma', lastName: 'Wilson' },
  ];

  for (const customerInfo of customerData) {
    const customerPassword = await bcrypt.hash('Customer123!@#', 12);

    const customer = await prisma.user.upsert({
      where: { email: customerInfo.email },
      update: {},
      create: {
        email: customerInfo.email,
        password: customerPassword,
        firstName: customerInfo.firstName,
        lastName: customerInfo.lastName,
        role: 'CUSTOMER',
        status: 'ACTIVE',
        emailVerified: true,
      },
    });

    customers.push(customer);
    logger.info(`Customer user created: ${customer.email}`);
  }

  return { admin, sellers, customers };
}

async function seedCategories() {
  logger.info('Seeding categories...');

  const categories = [
    {
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronic devices and accessories',
      image: '/uploads/categories/electronics.jpg',
      children: [
        { name: 'Smartphones', slug: 'smartphones', description: 'Mobile phones and accessories', image: '/uploads/categories/smartphones.jpg' },
        { name: 'Laptops', slug: 'laptops', description: 'Laptops and computer accessories', image: '/uploads/categories/laptops.jpg' },
        { name: 'Headphones', slug: 'headphones', description: 'Audio devices and headphones', image: '/uploads/categories/headphones.jpg' },
        { name: 'Tablets', slug: 'tablets', description: 'Tablets and e-readers', image: '/uploads/categories/tablets.jpg' },
        { name: 'Smart Watches', slug: 'smart-watches', description: 'Wearable technology', image: '/uploads/categories/smartwatches.jpg' },
        { name: 'Gaming', slug: 'gaming', description: 'Gaming consoles and accessories', image: '/uploads/categories/gaming.jpg' },
      ]
    },
    {
      name: 'Clothing & Fashion',
      slug: 'clothing-fashion',
      description: 'Fashion and apparel for all',
      image: '/uploads/categories/clothing.jpg',
      children: [
        { name: 'Men\'s Clothing', slug: 'mens-clothing', description: 'Men\'s fashion and apparel', image: '/uploads/categories/mens-clothing.jpg' },
        { name: 'Women\'s Clothing', slug: 'womens-clothing', description: 'Women\'s fashion and apparel', image: '/uploads/categories/womens-clothing.jpg' },
        { name: 'Shoes', slug: 'shoes', description: 'Footwear for all occasions', image: '/uploads/categories/shoes.jpg' },
        { name: 'Accessories', slug: 'accessories', description: 'Fashion accessories and jewelry', image: '/uploads/categories/accessories.jpg' },
        { name: 'Bags & Luggage', slug: 'bags-luggage', description: 'Bags, backpacks, and luggage', image: '/uploads/categories/bags.jpg' },
      ]
    },
    {
      name: 'Home & Garden',
      slug: 'home-garden',
      description: 'Home improvement and garden supplies',
      image: '/uploads/categories/home-garden.jpg',
      children: [
        { name: 'Furniture', slug: 'furniture', description: 'Home and office furniture', image: '/uploads/categories/furniture.jpg' },
        { name: 'Kitchen & Dining', slug: 'kitchen-dining', description: 'Kitchen appliances and dining essentials', image: '/uploads/categories/kitchen.jpg' },
        { name: 'Garden & Outdoor', slug: 'garden-outdoor', description: 'Garden tools and outdoor equipment', image: '/uploads/categories/garden.jpg' },
        { name: 'Home Decor', slug: 'home-decor', description: 'Decorative items and home accessories', image: '/uploads/categories/home-decor.jpg' },
        { name: 'Bedding & Bath', slug: 'bedding-bath', description: 'Bedroom and bathroom essentials', image: '/uploads/categories/bedding-bath.jpg' },
      ]
    },
    {
      name: 'Sports & Outdoors',
      slug: 'sports-outdoors',
      description: 'Sports equipment and outdoor gear',
      image: '/uploads/categories/sports.jpg',
      children: [
        { name: 'Fitness Equipment', slug: 'fitness-equipment', description: 'Home gym and fitness gear', image: '/uploads/categories/fitness.jpg' },
        { name: 'Outdoor Recreation', slug: 'outdoor-recreation', description: 'Camping, hiking, and outdoor activities', image: '/uploads/categories/outdoor.jpg' },
        { name: 'Sports Apparel', slug: 'sports-apparel', description: 'Athletic clothing and footwear', image: '/uploads/categories/sports-apparel.jpg' },
      ]
    },
    {
      name: 'Beauty & Personal Care',
      slug: 'beauty-personal-care',
      description: 'Beauty products and personal care items',
      image: '/uploads/categories/beauty.jpg',
      children: [
        { name: 'Skincare', slug: 'skincare', description: 'Skincare products and treatments', image: '/uploads/categories/skincare.jpg' },
        { name: 'Makeup', slug: 'makeup', description: 'Cosmetics and makeup products', image: '/uploads/categories/makeup.jpg' },
        { name: 'Hair Care', slug: 'hair-care', description: 'Hair care and styling products', image: '/uploads/categories/haircare.jpg' },
        { name: 'Fragrances', slug: 'fragrances', description: 'Perfumes and fragrances', image: '/uploads/categories/fragrances.jpg' },
      ]
    },
    {
      name: 'Books & Media',
      slug: 'books-media',
      description: 'Books, movies, music, and digital media',
      image: '/uploads/categories/books.jpg',
      children: [
        { name: 'Books', slug: 'books', description: 'Physical and digital books', image: '/uploads/categories/books-sub.jpg' },
        { name: 'Movies & TV', slug: 'movies-tv', description: 'Movies, TV shows, and entertainment', image: '/uploads/categories/movies.jpg' },
        { name: 'Music', slug: 'music', description: 'Music albums and instruments', image: '/uploads/categories/music.jpg' },
      ]
    }
  ];

  for (const categoryData of categories) {
    const { children, ...parentData } = categoryData;

    const parentCategory = await prisma.category.upsert({
      where: { slug: parentData.slug },
      update: {},
      create: parentData,
    });

    logger.info(`Parent category created: ${parentCategory.name}`);

    if (children) {
      for (const childData of children) {
        const childCategory = await prisma.category.upsert({
          where: { slug: childData.slug },
          update: {},
          create: {
            ...childData,
            parentId: parentCategory.id,
          },
        });

        logger.info(`Child category created: ${childCategory.name}`);
      }
    }
  }
}

async function seedProducts(users) {
  logger.info('Seeding products...');

  // Get all categories for product assignment
  const categories = await prisma.category.findMany({
    where: { parentId: { not: null } } // Only subcategories
  });

  if (categories.length === 0) {
    logger.warn('No subcategories found, skipping product seeding');
    return;
  }

  // Define comprehensive product data with various brands and price ranges
  const productTemplates = [
    // Electronics - Smartphones
    {
      name: 'iPhone 15 Pro Max',
      description: 'The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system. Features include 5x telephoto zoom, Action Button, and USB-C connectivity.',
      shortDescription: 'Latest iPhone with titanium design and A17 Pro chip',
      price: 1199.99,
      comparePrice: 1299.99,
      brand: 'Apple',
      images: ['/uploads/products/iphone-15-pro-max-1.jpg', '/uploads/products/iphone-15-pro-max-2.jpg', '/uploads/products/iphone-15-pro-max-3.jpg'],
      tags: ['smartphone', 'apple', 'ios', 'premium', '5g', 'camera'],
      category: 'smartphones',
      featured: true,
      quantity: 25,
    },
    {
      name: 'Samsung Galaxy S24 Ultra',
      description: 'Premium Android smartphone with S Pen, 200MP camera, and AI-powered features. Built with titanium frame and Gorilla Glass Victus 2.',
      shortDescription: 'Premium Galaxy with S Pen and 200MP camera',
      price: 1099.99,
      comparePrice: 1199.99,
      brand: 'Samsung',
      images: ['/uploads/products/galaxy-s24-ultra-1.jpg', '/uploads/products/galaxy-s24-ultra-2.jpg'],
      tags: ['smartphone', 'samsung', 'android', 's-pen', 'camera'],
      category: 'smartphones',
      featured: true,
      quantity: 30,
    },
    {
      name: 'Google Pixel 8 Pro',
      description: 'AI-powered smartphone with Magic Eraser, Best Take, and Audio Magic Eraser. Features Google Tensor G3 chip and advanced computational photography.',
      shortDescription: 'AI-powered Pixel with advanced photography',
      price: 899.99,
      comparePrice: 999.99,
      brand: 'Google',
      images: ['/uploads/products/pixel-8-pro-1.jpg', '/uploads/products/pixel-8-pro-2.jpg'],
      tags: ['smartphone', 'google', 'android', 'ai', 'photography'],
      category: 'smartphones',
      quantity: 20,
    },
    // Electronics - Laptops
    {
      name: 'MacBook Pro 16-inch M3 Max',
      description: 'Professional laptop with M3 Max chip, up to 128GB unified memory, and stunning Liquid Retina XDR display. Perfect for creative professionals.',
      shortDescription: 'Professional MacBook with M3 Max chip',
      price: 3499.99,
      comparePrice: 3699.99,
      brand: 'Apple',
      images: ['/uploads/products/macbook-pro-16-1.jpg', '/uploads/products/macbook-pro-16-2.jpg'],
      tags: ['laptop', 'apple', 'macbook', 'professional', 'm3-max'],
      category: 'laptops',
      featured: true,
      quantity: 15,
    },
    {
      name: 'Dell XPS 13 Plus',
      description: 'Ultra-portable laptop with 13th Gen Intel Core processors, InfinityEdge display, and premium carbon fiber build.',
      shortDescription: 'Ultra-portable XPS with premium design',
      price: 1299.99,
      comparePrice: 1399.99,
      brand: 'Dell',
      images: ['/uploads/products/dell-xps-13-1.jpg', '/uploads/products/dell-xps-13-2.jpg'],
      tags: ['laptop', 'dell', 'ultrabook', 'portable', 'intel'],
      category: 'laptops',
      quantity: 25,
    },
    // Electronics - Headphones
    {
      name: 'Sony WH-1000XM5',
      description: 'Industry-leading noise canceling headphones with exceptional sound quality, 30-hour battery life, and crystal-clear call quality.',
      shortDescription: 'Premium noise-canceling headphones',
      price: 399.99,
      comparePrice: 449.99,
      brand: 'Sony',
      images: ['/uploads/products/sony-wh1000xm5-1.jpg', '/uploads/products/sony-wh1000xm5-2.jpg'],
      tags: ['headphones', 'sony', 'noise-canceling', 'wireless', 'premium'],
      category: 'headphones',
      featured: true,
      quantity: 40,
    },
    {
      name: 'Apple AirPods Pro (2nd Gen)',
      description: 'Advanced noise cancellation, Adaptive Transparency, and personalized Spatial Audio. Features H2 chip for enhanced performance.',
      shortDescription: 'Advanced AirPods with H2 chip',
      price: 249.99,
      comparePrice: 279.99,
      brand: 'Apple',
      images: ['/uploads/products/airpods-pro-2-1.jpg', '/uploads/products/airpods-pro-2-2.jpg'],
      tags: ['earbuds', 'apple', 'noise-canceling', 'wireless', 'h2-chip'],
      category: 'headphones',
      quantity: 50,
    }
  ];

  // Create products with random seller assignment
  const createdProducts = [];
  for (const template of productTemplates) {
    const category = categories.find(cat => cat.slug === template.category);
    if (!category) continue;

    // Assign random seller
    const randomSeller = users.sellers[Math.floor(Math.random() * users.sellers.length)];

    // Generate unique slug and SKU
    const slug = template.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    const sku = `${template.brand.toUpperCase()}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    const productData = {
      name: template.name,
      slug,
      description: template.description,
      shortDescription: template.shortDescription,
      sku,
      price: template.price,
      comparePrice: template.comparePrice,
      quantity: template.quantity,
      images: template.images,
      status: 'APPROVED',
      featured: template.featured || false,
      tags: template.tags,
      sellerId: randomSeller.id,
      categoryId: category.id,
    };

    const product = await prisma.product.upsert({
      where: { slug },
      update: {},
      create: productData,
    });

    createdProducts.push(product);
    logger.info(`Product created: ${product.name}`);
  }

  return createdProducts;
}

async function seedMoreProducts(users) {
  logger.info('Seeding additional products...');

  const categories = await prisma.category.findMany({
    where: { parentId: { not: null } }
  });

  const additionalProducts = [
    // Clothing - Men's
    {
      name: 'Premium Cotton Polo Shirt',
      description: 'Classic fit polo shirt made from 100% premium cotton. Perfect for casual and business casual occasions.',
      shortDescription: 'Premium cotton polo shirt',
      price: 49.99,
      comparePrice: 59.99,
      brand: 'Ralph Lauren',
      images: ['/uploads/products/polo-shirt-1.jpg', '/uploads/products/polo-shirt-2.jpg'],
      tags: ['clothing', 'mens', 'polo', 'cotton', 'casual'],
      category: 'mens-clothing',
      quantity: 75,
    },
    {
      name: 'Slim Fit Jeans',
      description: 'Modern slim fit jeans with stretch denim for comfort and style. Available in multiple washes.',
      shortDescription: 'Comfortable slim fit jeans',
      price: 79.99,
      comparePrice: 89.99,
      brand: 'Levi\'s',
      images: ['/uploads/products/jeans-1.jpg', '/uploads/products/jeans-2.jpg'],
      tags: ['clothing', 'mens', 'jeans', 'denim', 'slim-fit'],
      category: 'mens-clothing',
      quantity: 60,
    },
    // Clothing - Women's
    {
      name: 'Floral Summer Dress',
      description: 'Elegant floral print dress perfect for summer occasions. Made from breathable fabric with a flattering fit.',
      shortDescription: 'Elegant floral summer dress',
      price: 89.99,
      comparePrice: 109.99,
      brand: 'Zara',
      images: ['/uploads/products/summer-dress-1.jpg', '/uploads/products/summer-dress-2.jpg'],
      tags: ['clothing', 'womens', 'dress', 'floral', 'summer'],
      category: 'womens-clothing',
      featured: true,
      quantity: 45,
    },
    {
      name: 'Cashmere Blend Sweater',
      description: 'Luxurious cashmere blend sweater with a soft feel and elegant drape. Perfect for layering.',
      shortDescription: 'Luxurious cashmere blend sweater',
      price: 129.99,
      comparePrice: 149.99,
      brand: 'J.Crew',
      images: ['/uploads/products/cashmere-sweater-1.jpg', '/uploads/products/cashmere-sweater-2.jpg'],
      tags: ['clothing', 'womens', 'sweater', 'cashmere', 'luxury'],
      category: 'womens-clothing',
      quantity: 35,
    },
    // Shoes
    {
      name: 'Running Sneakers',
      description: 'High-performance running shoes with advanced cushioning and breathable mesh upper. Perfect for daily runs.',
      shortDescription: 'High-performance running shoes',
      price: 149.99,
      comparePrice: 169.99,
      brand: 'Nike',
      images: ['/uploads/products/running-shoes-1.jpg', '/uploads/products/running-shoes-2.jpg'],
      tags: ['shoes', 'running', 'athletic', 'nike', 'performance'],
      category: 'shoes',
      featured: true,
      quantity: 80,
    },
    {
      name: 'Leather Oxford Shoes',
      description: 'Classic leather oxford shoes handcrafted with premium materials. Perfect for formal occasions.',
      shortDescription: 'Classic leather oxford shoes',
      price: 199.99,
      comparePrice: 229.99,
      brand: 'Cole Haan',
      images: ['/uploads/products/oxford-shoes-1.jpg', '/uploads/products/oxford-shoes-2.jpg'],
      tags: ['shoes', 'formal', 'leather', 'oxford', 'dress'],
      category: 'shoes',
      quantity: 40,
    },
    // Home & Kitchen
    {
      name: 'Stainless Steel Cookware Set',
      description: '10-piece professional cookware set with tri-ply construction for even heat distribution. Dishwasher safe.',
      shortDescription: 'Professional 10-piece cookware set',
      price: 299.99,
      comparePrice: 349.99,
      brand: 'All-Clad',
      images: ['/uploads/products/cookware-set-1.jpg', '/uploads/products/cookware-set-2.jpg'],
      tags: ['kitchen', 'cookware', 'stainless-steel', 'professional'],
      category: 'kitchen-dining',
      featured: true,
      quantity: 25,
    },
    {
      name: 'Ergonomic Office Chair',
      description: 'Premium ergonomic office chair with lumbar support, adjustable height, and breathable mesh back.',
      shortDescription: 'Premium ergonomic office chair',
      price: 399.99,
      comparePrice: 449.99,
      brand: 'Herman Miller',
      images: ['/uploads/products/office-chair-1.jpg', '/uploads/products/office-chair-2.jpg'],
      tags: ['furniture', 'office', 'chair', 'ergonomic', 'professional'],
      category: 'furniture',
      quantity: 20,
    },
    // Beauty
    {
      name: 'Anti-Aging Serum',
      description: 'Advanced anti-aging serum with retinol and hyaluronic acid. Reduces fine lines and improves skin texture.',
      shortDescription: 'Advanced anti-aging serum',
      price: 79.99,
      comparePrice: 89.99,
      brand: 'The Ordinary',
      images: ['/uploads/products/anti-aging-serum-1.jpg', '/uploads/products/anti-aging-serum-2.jpg'],
      tags: ['beauty', 'skincare', 'anti-aging', 'serum', 'retinol'],
      category: 'skincare',
      quantity: 100,
    },
    {
      name: 'Professional Makeup Brush Set',
      description: 'Complete 15-piece makeup brush set with synthetic bristles and ergonomic handles. Includes carrying case.',
      shortDescription: 'Professional 15-piece brush set',
      price: 59.99,
      comparePrice: 79.99,
      brand: 'Morphe',
      images: ['/uploads/products/makeup-brushes-1.jpg', '/uploads/products/makeup-brushes-2.jpg'],
      tags: ['beauty', 'makeup', 'brushes', 'professional', 'set'],
      category: 'makeup',
      quantity: 65,
    }
  ];

  const createdProducts = [];
  for (const template of additionalProducts) {
    const category = categories.find(cat => cat.slug === template.category);
    if (!category) continue;

    const randomSeller = users.sellers[Math.floor(Math.random() * users.sellers.length)];
    const slug = template.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    const sku = `${template.brand.replace(/[^a-zA-Z0-9]/g, '').toUpperCase()}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    const productData = {
      name: template.name,
      slug,
      description: template.description,
      shortDescription: template.shortDescription,
      sku,
      price: template.price,
      comparePrice: template.comparePrice,
      quantity: template.quantity,
      images: template.images,
      status: 'APPROVED',
      featured: template.featured || false,
      tags: template.tags,
      sellerId: randomSeller.id,
      categoryId: category.id,
    };

    const product = await prisma.product.upsert({
      where: { slug },
      update: {},
      create: productData,
    });

    createdProducts.push(product);
    logger.info(`Additional product created: ${product.name}`);
  }

  return createdProducts;
}

async function seedAddresses(users) {
  logger.info('Seeding addresses...');

  const addressTemplates = [
    {
      firstName: 'Jane',
      lastName: 'Customer',
      company: null,
      addressLine1: '123 Main Street',
      addressLine2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'United States',
      phone: '******-0123',
      type: 'shipping',
      isDefault: true,
    },
    {
      firstName: 'Jane',
      lastName: 'Customer',
      company: 'Tech Corp',
      addressLine1: '456 Business Ave',
      addressLine2: 'Suite 200',
      city: 'New York',
      state: 'NY',
      postalCode: '10002',
      country: 'United States',
      phone: '******-0124',
      type: 'billing',
      isDefault: false,
    }
  ];

  for (const customer of users.customers) {
    for (const addressTemplate of addressTemplates) {
      const address = await prisma.address.create({
        data: {
          ...addressTemplate,
          firstName: customer.firstName,
          lastName: customer.lastName,
          userId: customer.id,
        },
      });
      logger.info(`Address created for ${customer.firstName} ${customer.lastName}`);
    }
  }
}

async function seedOrders(users, products) {
  logger.info('Seeding orders...');

  const orderStatuses = ['PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED'];
  const paymentStatuses = ['PENDING', 'COMPLETED'];

  for (let i = 0; i < users.customers.length; i++) {
    const customer = users.customers[i];

    // Get customer's default shipping address
    const shippingAddress = await prisma.address.findFirst({
      where: {
        userId: customer.id,
        type: 'shipping',
        isDefault: true
      }
    });

    if (!shippingAddress) continue;

    // Create 2-4 orders per customer
    const orderCount = Math.floor(Math.random() * 3) + 2;

    for (let j = 0; j < orderCount; j++) {
      const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
      const orderStatus = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
      const paymentStatus = orderStatus === 'PENDING' ? 'PENDING' : 'COMPLETED';

      // Select 1-3 random products for this order
      const orderProductCount = Math.floor(Math.random() * 3) + 1;
      const selectedProducts = [];

      for (let k = 0; k < orderProductCount; k++) {
        const randomProduct = products[Math.floor(Math.random() * products.length)];
        const quantity = Math.floor(Math.random() * 3) + 1;
        selectedProducts.push({ product: randomProduct, quantity });
      }

      // Calculate totals
      const subtotal = selectedProducts.reduce((sum, item) =>
        sum + (parseFloat(item.product.price) * item.quantity), 0
      );
      const taxAmount = subtotal * 0.08; // 8% tax
      const shippingAmount = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
      const totalAmount = subtotal + taxAmount + shippingAmount;

      // Create order
      const order = await prisma.order.create({
        data: {
          orderNumber,
          userId: customer.id,
          status: orderStatus,
          paymentStatus,
          subtotal,
          taxAmount,
          shippingAmount,
          discountAmount: 0,
          totalAmount,
          currency: 'USD',
          paymentMethod: 'credit_card',
          shippingAddressId: shippingAddress.id,
          billingAddressId: shippingAddress.id,
          notes: j === 0 ? 'Please leave at front door' : null,
          trackingNumber: orderStatus === 'SHIPPED' || orderStatus === 'DELIVERED' ?
            `TRK${Math.floor(Math.random() * 1000000000)}` : null,
          shippedAt: orderStatus === 'SHIPPED' || orderStatus === 'DELIVERED' ?
            new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) : null,
          deliveredAt: orderStatus === 'DELIVERED' ?
            new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000) : null,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
        },
      });

      // Create order items
      for (const item of selectedProducts) {
        await prisma.orderItem.create({
          data: {
            orderId: order.id,
            productId: item.product.id,
            quantity: item.quantity,
            unitPrice: parseFloat(item.product.price),
            totalPrice: parseFloat(item.product.price) * item.quantity,
          },
        });
      }

      logger.info(`Order created: ${orderNumber} for ${customer.firstName} ${customer.lastName}`);
    }
  }
}

async function seedReviews(users, products) {
  logger.info('Seeding reviews...');

  const reviewTemplates = [
    {
      rating: 5,
      title: 'Excellent product!',
      comment: 'This product exceeded my expectations. Great quality and fast shipping. Highly recommended!',
    },
    {
      rating: 4,
      title: 'Very good quality',
      comment: 'Good product overall. Minor issues but nothing major. Would buy again.',
    },
    {
      rating: 5,
      title: 'Perfect!',
      comment: 'Exactly what I was looking for. Perfect fit and excellent build quality.',
    },
    {
      rating: 3,
      title: 'Decent product',
      comment: 'It\'s okay. Does what it\'s supposed to do but nothing special.',
    },
    {
      rating: 4,
      title: 'Good value for money',
      comment: 'Great value for the price. Good quality and arrived quickly.',
    },
    {
      rating: 5,
      title: 'Love it!',
      comment: 'Amazing product! Great design and functionality. Will definitely recommend to friends.',
    },
    {
      rating: 2,
      title: 'Not as expected',
      comment: 'Product quality is not as good as described. Had some issues with it.',
    },
    {
      rating: 4,
      title: 'Satisfied with purchase',
      comment: 'Good product, fast delivery. Minor packaging issues but product was fine.',
    }
  ];

  // Create reviews for random products from random customers
  for (let i = 0; i < 50; i++) { // Create 50 random reviews
    const randomCustomer = users.customers[Math.floor(Math.random() * users.customers.length)];
    const randomProduct = products[Math.floor(Math.random() * products.length)];
    const randomReview = reviewTemplates[Math.floor(Math.random() * reviewTemplates.length)];

    // Check if this customer already reviewed this product
    const existingReview = await prisma.review.findUnique({
      where: {
        userId_productId_orderId: {
          userId: randomCustomer.id,
          productId: randomProduct.id,
          orderId: null,
        }
      }
    });

    if (existingReview) continue; // Skip if review already exists

    const review = await prisma.review.create({
      data: {
        userId: randomCustomer.id,
        productId: randomProduct.id,
        rating: randomReview.rating,
        title: randomReview.title,
        comment: randomReview.comment,
        isVerified: Math.random() > 0.3, // 70% verified reviews
        isApproved: true,
        helpfulCount: Math.floor(Math.random() * 20),
        createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000), // Random date within last 60 days
      },
    });

    logger.info(`Review created for product: ${randomProduct.name}`);
  }
}

async function seedCoupons() {
  logger.info('Seeding coupons...');

  const coupons = [
    {
      code: 'WELCOME10',
      name: 'Welcome Discount',
      description: '10% off your first order',
      type: 'percentage',
      value: 10,
      minimumAmount: 50,
      maximumDiscount: 100,
      usageLimit: 1000,
      isActive: true,
      startsAt: new Date(),
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
    {
      code: 'SAVE20',
      name: 'Save $20',
      description: '$20 off orders over $100',
      type: 'fixed_amount',
      value: 20,
      minimumAmount: 100,
      usageLimit: 500,
      isActive: true,
      startsAt: new Date(),
      expiresAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
    },
    {
      code: 'FREESHIP',
      name: 'Free Shipping',
      description: 'Free shipping on any order',
      type: 'fixed_amount',
      value: 9.99,
      minimumAmount: 0,
      usageLimit: 200,
      isActive: true,
      startsAt: new Date(),
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 3 months from now
    }
  ];

  for (const couponData of coupons) {
    const coupon = await prisma.coupon.upsert({
      where: { code: couponData.code },
      update: {},
      create: couponData,
    });

    logger.info(`Coupon created: ${coupon.code}`);
  }
}

async function main() {
  try {
    logger.info('Starting comprehensive database seeding...');

    // Seed users first
    const users = await seedUsers();

    // Seed categories
    await seedCategories();

    // Seed products
    const products = await seedProducts(users);
    const additionalProducts = await seedMoreProducts(users);
    const allProducts = [...products, ...additionalProducts];

    // Seed addresses for customers
    await seedAddresses(users);

    // Seed orders
    await seedOrders(users, allProducts);

    // Seed reviews
    await seedReviews(users, allProducts);

    // Seed coupons
    await seedCoupons();

    logger.info('🎉 Comprehensive database seeding completed successfully!');
    logger.info(`📊 Seeded data summary:`);
    logger.info(`   - Users: ${users.customers.length + users.sellers.length + 1} (${users.customers.length} customers, ${users.sellers.length} sellers, 1 admin)`);
    logger.info(`   - Products: ${allProducts.length}`);
    logger.info(`   - Categories: Multiple with subcategories`);
    logger.info(`   - Orders: Multiple per customer`);
    logger.info(`   - Reviews: ~50 product reviews`);
    logger.info(`   - Coupons: 3 promotional codes`);

  } catch (error) {
    logger.error('Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeder if called directly
if (require.main === module) {
  main()
    .catch((error) => {
      logger.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = main;

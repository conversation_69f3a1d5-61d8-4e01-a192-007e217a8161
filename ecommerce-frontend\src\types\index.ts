// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: {
    message: string;
    details?: any;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: PaginationMeta;
  };
}

// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  role: 'CUSTOMER' | 'SELLER' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  emailVerified: boolean;
  phoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: 'CUSTOMER' | 'SELLER';
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Address Types
export interface Address {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  type: 'shipping' | 'billing';
  createdAt: string;
  updatedAt: string;
}

// Category Types
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  isActive: boolean;
  sortOrder: number;
  parent?: Category;
  children?: Category[];
  _count?: {
    products: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Product Types
export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  sku: string;
  price?: number;
  quantity: number;
  options: Record<string, any>;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  sku: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  trackQuantity: boolean;
  quantity: number;
  allowBackorder: boolean;
  weight?: number;
  dimensions?: string;
  images: string[];
  status: 'DRAFT' | 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' | 'INACTIVE';
  featured: boolean;
  tags: string[];
  metaTitle?: string;
  metaDescription?: string;
  sellerId: string;
  categoryId: string;
  category?: Category;
  seller?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  variants?: ProductVariant[];
  reviews?: Review[];
  averageRating?: number;
  reviewCount?: number;
  _count?: {
    reviews: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Cart Types
export interface CartItem {
  id: string;
  userId: string;
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  isAvailable: boolean;
  product: {
    id: string;
    name: string;
    slug: string;
    price: number;
    images: string[];
    status: string;
    quantity: number;
    trackQuantity: boolean;
    allowBackorder: boolean;
    seller: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
  variant?: {
    id: string;
    name: string;
    price: number;
    quantity: number;
    options: Record<string, any>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CartSummary {
  totalItems: number;
  subtotal: number;
  itemCount: number;
}

// Order Types
export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  product: Product;
  variant?: ProductVariant;
  createdAt: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
  paymentStatus: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  paymentMethod?: string;
  paymentId?: string;
  shippingAddressId: string;
  billingAddressId?: string;
  notes?: string;
  trackingNumber?: string;
  shippedAt?: string;
  deliveredAt?: string;
  items: OrderItem[];
  shippingAddress: Address;
  createdAt: string;
  updatedAt: string;
}

// Review Types
export interface Review {
  id: string;
  userId: string;
  productId: string;
  orderId?: string;
  rating: number;
  title?: string;
  comment?: string;
  images: string[];
  isVerified: boolean;
  isApproved: boolean;
  helpfulCount: number;
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Search and Filter Types
export interface ProductFilters {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'createdAt' | 'price' | 'name' | 'featured' | 'relevance' | 'price_asc' | 'price_desc' | 'newest' | 'oldest';
  sortOrder?: 'asc' | 'desc';
  featured?: boolean;
  sellerId?: string;
  status?: string;
  page?: number;
  limit?: number;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface UIState {
  sidebarOpen: boolean;
  cartOpen: boolean;
  searchOpen: boolean;
  theme: 'light' | 'dark';
}

// Form Types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface AddressFormData {
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  type: 'shipping' | 'billing';
  isDefault?: boolean;
}

export interface Address {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  type: 'shipping' | 'billing';
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  product: Product;
  variant?: ProductVariant;
  createdAt: string;
}

export interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'CUSTOMER' | 'SELLER';
  agreeToTerms: boolean;
}

export interface ProductFormData {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  comparePrice?: number;
  categoryId: string;
  sku?: string;
  quantity: number;
  trackQuantity: boolean;
  allowBackorder: boolean;
  weight?: number;
  dimensions?: string;
  images: string[];
  tags: string[];
  metaTitle?: string;
  metaDescription?: string;
}

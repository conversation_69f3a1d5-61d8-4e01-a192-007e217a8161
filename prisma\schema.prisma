// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  CUSTOMER
  SELLER
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum ProductStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  INACTIVE
}

model User {
  id                String      @id @default(cuid())
  email             String      @unique
  phone             String?     @unique
  password          String
  firstName         String
  lastName          String
  avatar            String?
  role              UserRole    @default(CUSTOMER)
  status            UserStatus  @default(ACTIVE)
  emailVerified     Boolean     @default(false)
  phoneVerified     Boolean     @default(false)
  emailVerifyToken  String?
  phoneVerifyToken  String?
  resetPasswordToken String?
  resetPasswordExpires DateTime?
  lastLoginAt       DateTime?
  loginAttempts     Int         @default(0)
  lockUntil         DateTime?
  twoFactorSecret   String?
  twoFactorEnabled  Boolean     @default(false)
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relationships
  addresses         Address[]
  orders            Order[]
  reviews           Review[]
  cart              CartItem[]
  wishlist          WishlistItem[]
  products          Product[]   @relation("SellerProducts")
  refreshTokens     RefreshToken[]
  auditLogs         AuditLog[]

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model Address {
  id           String  @id @default(cuid())
  userId       String
  firstName    String
  lastName     String
  company      String?
  addressLine1 String
  addressLine2 String?
  city         String
  state        String
  postalCode   String
  country      String
  phone        String?
  isDefault    Boolean @default(false)
  type         String  @default("shipping") // shipping, billing
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  @@map("addresses")
}

model Category {
  id          String     @id @default(cuid())
  name        String
  slug        String     @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean    @default(true)
  sortOrder   Int        @default(0)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  parent     Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children   Category[] @relation("CategoryHierarchy")
  products   Product[]

  @@map("categories")
}

model Product {
  id              String        @id @default(cuid())
  name            String
  slug            String        @unique
  description     String
  shortDescription String?
  sku             String        @unique
  price           Decimal       @db.Decimal(10, 2)
  comparePrice    Decimal?      @db.Decimal(10, 2)
  costPrice       Decimal?      @db.Decimal(10, 2)
  trackQuantity   Boolean       @default(true)
  quantity        Int           @default(0)
  allowBackorder  Boolean       @default(false)
  weight          Decimal?      @db.Decimal(8, 2)
  dimensions      String?       // JSON string for length, width, height
  images          String[]      // Array of image URLs
  status          ProductStatus @default(DRAFT)
  featured        Boolean       @default(false)
  tags            String[]
  metaTitle       String?
  metaDescription String?
  sellerId        String
  categoryId      String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  seller       User           @relation("SellerProducts", fields: [sellerId], references: [id])
  category     Category       @relation(fields: [categoryId], references: [id])
  variants     ProductVariant[]
  orderItems   OrderItem[]
  reviews      Review[]
  cartItems    CartItem[]
  wishlistItems WishlistItem[]

  @@map("products")
}

model ProductVariant {
  id        String   @id @default(cuid())
  productId String
  name      String   // e.g., "Red - Large", "Blue - Medium"
  sku       String   @unique
  price     Decimal? @db.Decimal(10, 2)
  quantity  Int      @default(0)
  options   Json     // JSON object for variant options like {color: "red", size: "large"}
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  product    Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]
  cartItems  CartItem[]

  @@map("product_variants")
}

model CartItem {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  variantId   String?
  quantity    Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("cart_items")
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

model Order {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  userId          String
  status          OrderStatus   @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  subtotal        Decimal       @db.Decimal(10, 2)
  taxAmount       Decimal       @db.Decimal(10, 2) @default(0)
  shippingAmount  Decimal       @db.Decimal(10, 2) @default(0)
  discountAmount  Decimal       @db.Decimal(10, 2) @default(0)
  totalAmount     Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  paymentMethod   String?
  paymentId       String?       // Stripe payment intent ID
  shippingAddressId String
  billingAddressId  String?
  notes           String?
  trackingNumber  String?
  shippedAt       DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  user            User        @relation(fields: [userId], references: [id])
  shippingAddress Address     @relation(fields: [shippingAddressId], references: [id])
  items           OrderItem[]
  coupons         OrderCoupon[]

  @@map("orders")
}

model OrderItem {
  id          String   @id @default(cuid())
  orderId     String
  productId   String
  variantId   String?
  quantity    Int
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  createdAt   DateTime @default(now())

  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model Coupon {
  id              String   @id @default(cuid())
  code            String   @unique
  name            String
  description     String?
  type            String   // percentage, fixed_amount
  value           Decimal  @db.Decimal(10, 2)
  minimumAmount   Decimal? @db.Decimal(10, 2)
  maximumDiscount Decimal? @db.Decimal(10, 2)
  usageLimit      Int?
  usedCount       Int      @default(0)
  isActive        Boolean  @default(true)
  startsAt        DateTime
  expiresAt       DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  orders OrderCoupon[]

  @@map("coupons")
}

model OrderCoupon {
  id            String  @id @default(cuid())
  orderId       String
  couponId      String
  discountAmount Decimal @db.Decimal(10, 2)

  order  Order  @relation(fields: [orderId], references: [id], onDelete: Cascade)
  coupon Coupon @relation(fields: [couponId], references: [id])

  @@unique([orderId, couponId])
  @@map("order_coupons")
}

model Review {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  orderId     String?  // To ensure verified purchase
  rating      Int      // 1-5 stars
  title       String?
  comment     String?
  images      String[] // Array of review image URLs
  isVerified  Boolean  @default(false)
  isApproved  Boolean  @default(false)
  helpfulCount Int     @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, orderId])
  @@map("reviews")
}

model AuditLog {
  id        String   @id @default(cuid())
  event     String   // Event type (e.g., user.login.success)
  userId    String?
  sessionId String?
  ipAddress String?
  userAgent String?
  resource  String?  // Resource being accessed
  action    String?  // Action performed
  result    String   @default("success") // success, failed, blocked, etc.
  metadata  String?  // JSON string for additional data
  severity  String   @default("info") // info, warning, high
  timestamp DateTime @default(now())

  user User? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

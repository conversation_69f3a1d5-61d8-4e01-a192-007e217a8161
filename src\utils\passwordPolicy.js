const securityConfig = require('../config/security');
const logger = require('./logger');

const config = securityConfig.getConfig();

/**
 * Password Policy Validator
 */
class PasswordPolicy {
  /**
   * Validate password against policy
   */
  static validate(password) {
    const errors = [];
    const policy = config.password;

    // Check minimum length
    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }

    // Check for uppercase letters
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    // Check for lowercase letters
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    // Check for numbers
    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    // Check for special characters
    if (policy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak patterns
    const weakPatterns = [
      /^(.)\1+$/, // All same character
      /^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i, // Sequential
      /^(password|123456|qwerty|admin|user|guest|test|demo)/i // Common passwords
    ];

    for (const pattern of weakPatterns) {
      if (pattern.test(password)) {
        errors.push('Password contains common weak patterns');
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength: this.calculateStrength(password)
    };
  }

  /**
   * Calculate password strength score (0-100)
   */
  static calculateStrength(password) {
    let score = 0;
    
    // Length bonus
    score += Math.min(password.length * 4, 25);
    
    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/\d/.test(password)) score += 5;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    
    // Unique characters bonus
    const uniqueChars = new Set(password).size;
    score += Math.min(uniqueChars * 2, 20);
    
    // Pattern penalties
    if (/(.)\1{2,}/.test(password)) score -= 10; // Repeated characters
    if (/^(.{1,3})\1+$/.test(password)) score -= 20; // Repeated patterns
    if (/^[a-zA-Z]+$/.test(password)) score -= 5; // Only letters
    if (/^\d+$/.test(password)) score -= 10; // Only numbers
    
    // Common password penalty
    const commonPasswords = [
      'password', '123456', 'qwerty', 'admin', 'user', 'guest',
      'test', 'demo', 'welcome', 'login', 'master', 'secret'
    ];
    
    if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
      score -= 25;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get strength label
   */
  static getStrengthLabel(score) {
    if (score < 30) return 'Very Weak';
    if (score < 50) return 'Weak';
    if (score < 70) return 'Fair';
    if (score < 85) return 'Good';
    return 'Strong';
  }

  /**
   * Check if password has been compromised (basic check)
   */
  static async isCompromised(password) {
    // In a real implementation, you would check against a database of compromised passwords
    // For now, we'll just check against a small list of very common passwords
    const compromisedPasswords = [
      'password',
      '123456',
      '********9',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      'letmein',
      'welcome',
      'monkey',
      '********90',
      'iloveyou',
      'princess',
      'rockyou',
      '********'
    ];

    return compromisedPasswords.includes(password.toLowerCase());
  }

  /**
   * Generate password suggestions
   */
  static generateSuggestions() {
    const suggestions = [
      'Use a mix of uppercase and lowercase letters',
      'Include numbers and special characters',
      'Make it at least 12 characters long',
      'Avoid common words and patterns',
      'Consider using a passphrase with random words',
      'Don\'t reuse passwords from other accounts',
      'Use a password manager to generate and store strong passwords'
    ];

    return suggestions;
  }

  /**
   * Check password history to prevent reuse
   */
  static async checkPasswordHistory(userId, newPassword, historyLimit = 5) {
    try {
      // This would check against stored password hashes in the database
      // For security, we should store hashed versions of old passwords
      // Implementation would depend on your password history storage strategy
      
      // For now, return false (not in history)
      return false;
    } catch (error) {
      logger.error('Password history check failed:', error);
      return false;
    }
  }

  /**
   * Generate a secure random password
   */
  static generateSecurePassword(length = 16) {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * Validate password change request
   */
  static async validatePasswordChange(userId, currentPassword, newPassword, hashedCurrentPassword) {
    const bcrypt = require('bcryptjs');
    const errors = [];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, hashedCurrentPassword);
    if (!isCurrentPasswordValid) {
      errors.push('Current password is incorrect');
    }

    // Check if new password is same as current
    const isSamePassword = await bcrypt.compare(newPassword, hashedCurrentPassword);
    if (isSamePassword) {
      errors.push('New password must be different from current password');
    }

    // Validate new password policy
    const policyValidation = this.validate(newPassword);
    if (!policyValidation.isValid) {
      errors.push(...policyValidation.errors);
    }

    // Check if password is compromised
    const isCompromised = await this.isCompromised(newPassword);
    if (isCompromised) {
      errors.push('This password has been found in data breaches and cannot be used');
    }

    // Check password history
    const isInHistory = await this.checkPasswordHistory(userId, newPassword);
    if (isInHistory) {
      errors.push('Cannot reuse a recent password');
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength: policyValidation.strength
    };
  }
}

module.exports = PasswordPolicy;

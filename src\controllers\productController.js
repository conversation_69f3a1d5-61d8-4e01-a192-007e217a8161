const { prisma } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Generate unique slug from product name
 */
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

/**
 * Generate unique SKU
 */
const generateSKU = async (name) => {
  const baseSlug = generateSlug(name).toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  return `${baseSlug}-${timestamp}`;
};

/**
 * List products with filtering, sorting, and pagination
 */
const getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      search,
      minPrice,
      maxPrice,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      status = 'APPROVED',
      featured,
      sellerId
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    const where = {
      status: status === 'all' ? undefined : status,
      ...(category && {
        category: {
          slug: category
        }
      }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { has: search } }
        ]
      }),
      ...(minPrice && { price: { gte: parseFloat(minPrice) } }),
      ...(maxPrice && { price: { lte: parseFloat(maxPrice) } }),
      ...(featured !== undefined && { featured: featured === 'true' }),
      ...(sellerId && { sellerId })
    };

    // Build orderBy clause
    const orderBy = {};
    orderBy[sortBy] = sortOrder;

    const [products, total] = await Promise.all([
      prisma().product.findMany({
        where,
        skip,
        take,
        orderBy,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          variants: {
            select: {
              id: true,
              name: true,
              price: true,
              quantity: true
            }
          },
          _count: {
            select: {
              reviews: true
            }
          }
        }
      }),
      prisma().product.count({ where })
    ]);

    // Calculate average rating for each product
    const productsWithRating = await Promise.all(
      products.map(async (product) => {
        const avgRating = await prisma().review.aggregate({
          where: { productId: product.id },
          _avg: { rating: true }
        });

        return {
          ...product,
          averageRating: avgRating._avg.rating || 0,
          reviewCount: product._count.reviews
        };
      })
    );

    const totalPages = Math.ceil(total / take);

    res.json({
      success: true,
      data: {
        products: productsWithRating,
        pagination: {
          page: parseInt(page),
          limit: take,
          total,
          totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Get products error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get products'
      }
    });
  }
};

/**
 * Get single product details
 */
const getProduct = async (req, res) => {
  const { productId } = req.params;

  try {
    const product = await prisma().product.findUnique({
      where: { id: productId },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            parent: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        },
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        variants: true,
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        },
        _count: {
          select: {
            reviews: true
          }
        }
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found'
        }
      });
    }

    // Calculate average rating
    const avgRating = await prisma().review.aggregate({
      where: { productId: product.id },
      _avg: { rating: true }
    });

    // Track recently viewed (if user is authenticated)
    if (req.user) {
      // TODO: Implement recently viewed tracking
    }

    res.json({
      success: true,
      data: {
        product: {
          ...product,
          averageRating: avgRating._avg.rating || 0,
          reviewCount: product._count.reviews
        }
      }
    });

  } catch (error) {
    logger.error('Get product error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get product'
      }
    });
  }
};

/**
 * Search products
 */
const searchProducts = async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      limit = 20,
      category,
      minPrice,
      maxPrice,
      sortBy = 'relevance'
    } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Search query is required'
        }
      });
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build search conditions
    const searchConditions = {
      status: 'APPROVED',
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { shortDescription: { contains: query, mode: 'insensitive' } },
        { tags: { has: query } }
      ],
      ...(category && {
        category: {
          slug: category
        }
      }),
      ...(minPrice && { price: { gte: parseFloat(minPrice) } }),
      ...(maxPrice && { price: { lte: parseFloat(maxPrice) } })
    };

    // Build orderBy based on sortBy
    let orderBy = {};
    switch (sortBy) {
      case 'price_asc':
        orderBy = { price: 'asc' };
        break;
      case 'price_desc':
        orderBy = { price: 'desc' };
        break;
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'name':
        orderBy = { name: 'asc' };
        break;
      default:
        orderBy = { featured: 'desc' }; // relevance - featured first
    }

    const [products, total] = await Promise.all([
      prisma().product.findMany({
        where: searchConditions,
        skip,
        take,
        orderBy,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          _count: {
            select: {
              reviews: true
            }
          }
        }
      }),
      prisma().product.count({ where: searchConditions })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      success: true,
      data: {
        products,
        query,
        pagination: {
          page: parseInt(page),
          limit: take,
          total,
          totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    logger.error('Search products error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to search products'
      }
    });
  }
};

/**
 * Create new product (sellers only)
 */
const createProduct = async (req, res) => {
  try {
    const {
      name,
      description,
      shortDescription,
      price,
      comparePrice,
      costPrice,
      categoryId,
      quantity = 0,
      trackQuantity = true,
      allowBackorder = false,
      weight,
      dimensions,
      images = [],
      tags = [],
      metaTitle,
      metaDescription
    } = req.body;

    const sellerId = req.user.id;

    // Generate unique slug and SKU
    let slug = generateSlug(name);
    let sku = req.body.sku || await generateSKU(name);

    // Check if slug already exists
    const existingSlug = await prisma().product.findUnique({
      where: { slug }
    });

    if (existingSlug) {
      slug = `${slug}-${Date.now()}`;
    }

    // Check if SKU already exists
    const existingSku = await prisma().product.findUnique({
      where: { sku }
    });

    if (existingSku) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'SKU already exists'
        }
      });
    }

    // Verify category exists
    const category = await prisma().category.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Category not found'
        }
      });
    }

    // Create product
    const product = await prisma().product.create({
      data: {
        name,
        slug,
        description,
        shortDescription,
        sku,
        price: parseFloat(price),
        comparePrice: comparePrice ? parseFloat(comparePrice) : null,
        costPrice: costPrice ? parseFloat(costPrice) : null,
        trackQuantity,
        quantity: parseInt(quantity),
        allowBackorder,
        weight: weight ? parseFloat(weight) : null,
        dimensions,
        images,
        status: req.user.role === 'ADMIN' ? 'APPROVED' : 'PENDING_APPROVAL',
        tags,
        metaTitle,
        metaDescription,
        sellerId,
        categoryId,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    logger.info(`Product created: ${product.name} by ${req.user.email}`);

    res.status(201).json({
      success: true,
      data: {
        product
      }
    });

  } catch (error) {
    logger.error('Create product error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create product'
      }
    });
  }
};

/**
 * Update product (sellers/admins only)
 */
const updateProduct = async (req, res) => {
  const { productId } = req.params;
  const updateData = req.body;
  const userId = req.user.id;
  const userRole = req.user.role;

  try {
    // Get existing product
    const existingProduct = await prisma().product.findUnique({
      where: { id: productId }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found'
        }
      });
    }

    // Check permissions
    if (userRole !== 'ADMIN' && existingProduct.sellerId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'You can only update your own products'
        }
      });
    }

    // If updating name, regenerate slug
    if (updateData.name && updateData.name !== existingProduct.name) {
      let newSlug = generateSlug(updateData.name);

      const existingSlug = await prisma().product.findFirst({
        where: {
          slug: newSlug,
          id: { not: productId }
        }
      });

      if (existingSlug) {
        newSlug = `${newSlug}-${Date.now()}`;
      }

      updateData.slug = newSlug;
    }

    // Convert numeric fields
    if (updateData.price) updateData.price = parseFloat(updateData.price);
    if (updateData.comparePrice) updateData.comparePrice = parseFloat(updateData.comparePrice);
    if (updateData.costPrice) updateData.costPrice = parseFloat(updateData.costPrice);
    if (updateData.quantity) updateData.quantity = parseInt(updateData.quantity);
    if (updateData.weight) updateData.weight = parseFloat(updateData.weight);

    const updatedProduct = await prisma().product.update({
      where: { id: productId },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    logger.info(`Product updated: ${updatedProduct.name} by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        product: updatedProduct
      }
    });

  } catch (error) {
    logger.error('Update product error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update product'
      }
    });
  }
};

/**
 * Delete product (sellers/admins only)
 */
const deleteProduct = async (req, res) => {
  const { productId } = req.params;
  const userId = req.user.id;
  const userRole = req.user.role;

  try {
    // Get existing product
    const existingProduct = await prisma().product.findUnique({
      where: { id: productId }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Product not found'
        }
      });
    }

    // Check permissions
    if (userRole !== 'ADMIN' && existingProduct.sellerId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'You can only delete your own products'
        }
      });
    }

    // Check if product has orders
    const orderCount = await prisma().orderItem.count({
      where: { productId }
    });

    if (orderCount > 0) {
      // Don't delete, just mark as inactive
      await prisma().product.update({
        where: { id: productId },
        data: { status: 'INACTIVE' }
      });

      logger.info(`Product marked as inactive: ${existingProduct.name} by ${req.user.email}`);

      return res.json({
        success: true,
        data: {
          message: 'Product marked as inactive due to existing orders'
        }
      });
    }

    // Delete product
    await prisma().product.delete({
      where: { id: productId }
    });

    logger.info(`Product deleted: ${existingProduct.name} by ${req.user.email}`);

    res.json({
      success: true,
      data: {
        message: 'Product deleted successfully'
      }
    });

  } catch (error) {
    logger.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete product'
      }
    });
  }
};

module.exports = {
  getProducts,
  getProduct,
  searchProducts,
  createProduct,
  updateProduct,
  deleteProduct,
  generateSlug,
  generateSKU,
};
